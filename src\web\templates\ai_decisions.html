{% extends "base.html" %}

{% block title %}AI决策 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">AI决策</span>
{% endblock %}

{% block content %}
<div class="ai-decisions-container">
    <!-- AI引擎状态卡片 -->
    <div class="engine-status-cards">
        <div class="status-card">
            <div class="card-header">
                <h3>AI开仓引擎</h3>
                <div class="engine-status" id="openingEngineStatus">
                    <span class="status-indicator"></span>
                    <span class="status-text">检查中...</span>
                </div>
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <span class="stat-label">今日决策:</span>
                    <span class="stat-value" id="openingDecisionsToday">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">执行率:</span>
                    <span class="stat-value" id="openingExecutionRate">--%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均置信度:</span>
                    <span class="stat-value" id="openingAvgConfidence">--%</span>
                </div>
            </div>
        </div>

        <div class="status-card">
            <div class="card-header">
                <h3>AI持仓引擎</h3>
                <div class="engine-status" id="positionEngineStatus">
                    <span class="status-indicator"></span>
                    <span class="status-text">检查中...</span>
                </div>
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <span class="stat-label">今日决策:</span>
                    <span class="stat-value" id="positionDecisionsToday">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">执行率:</span>
                    <span class="stat-value" id="positionExecutionRate">--%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均置信度:</span>
                    <span class="stat-value" id="positionAvgConfidence">--%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和控制面板 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter"></i>
                筛选条件
            </h3>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshDecisions()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
                <button class="btn btn-primary" onclick="exportDecisions()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="filter-controls">
                <div class="filter-group">
                    <label>引擎类型:</label>
                    <select id="engineTypeFilter" class="form-control">
                        <option value="">全部</option>
                        <option value="opening">开仓引擎</option>
                        <option value="position">持仓引擎</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>交易对:</label>
                    <select id="symbolFilter" class="form-control">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>决策动作:</label>
                    <select id="actionFilter" class="form-control">
                        <option value="">全部</option>
                        <option value="open_long">开多</option>
                        <option value="open_short">开空</option>
                        <option value="hold">持有</option>
                        <option value="close_position">平仓</option>
                        <option value="no_action">无动作</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>置信度范围:</label>
                    <div class="confidence-range">
                        <input type="range" id="confidenceMin" min="0" max="100" value="0" class="range-input">
                        <span id="confidenceMinValue">0</span>
                        <span>-</span>
                        <input type="range" id="confidenceMax" min="0" max="100" value="100" class="range-input">
                        <span id="confidenceMaxValue">100</span>
                    </div>
                </div>
                <div class="filter-group">
                    <label>时间范围:</label>
                    <select id="timeRangeFilter" class="form-control">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h" selected>最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="30d">最近30天</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i>
                        应用筛选
                    </button>
                    <button class="btn btn-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI决策列表 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-brain"></i>
                AI决策历史
            </h3>
            <div class="decision-stats">
                <span class="stats-item">
                    总计: <strong id="totalDecisions">--</strong>
                </span>
                <span class="stats-item">
                    已执行: <strong id="executedDecisions">--</strong>
                </span>
                <span class="stats-item">
                    成功率: <strong id="successRate">--%</strong>
                </span>
            </div>
        </div>
        <div class="card-body">
            <div class="decisions-list" id="decisionsList">
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载AI决策数据中...</span>
                </div>
            </div>
            
            <!-- 分页控制 -->
            <div class="pagination-controls" id="paginationControls" style="display: none;">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="previousPage()">
                    <i class="fas fa-chevron-left"></i>
                    上一页
                </button>
                <span class="page-info">
                    第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                </span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="nextPage()">
                    下一页
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 决策详情模态框 -->
    <div class="modal-overlay" id="decisionDetailModal" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-header">
                <h3 id="decisionDetailTitle">AI决策详情</h3>
                <button class="modal-close" onclick="hideDecisionDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="decision-detail-content" id="decisionDetailContent">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideDecisionDetail()">关闭</button>
                <button class="btn btn-primary" id="copyDecisionBtn" onclick="copyDecisionData()">
                    <i class="fas fa-copy"></i>
                    复制数据
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* AI决策页面样式 */
.ai-decisions-container {
    padding: var(--spacing-lg);
}

.engine-status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.status-card {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.status-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-secondary);
}

.status-card h3 {
    font-size: 1.125rem;
    color: var(--text-primary);
    margin: 0;
}

.engine-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--color-warning);
    animation: pulse 2s infinite;
}

.status-indicator.running {
    background-color: var(--color-success);
}

.status-indicator.error {
    background-color: var(--color-error);
}

.status-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

.card-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-weight: 600;
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-control {
    padding: var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.confidence-range {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.range-input {
    flex: 1;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--color-info);
    border-radius: 50%;
    cursor: pointer;
}

.decision-stats {
    display: flex;
    gap: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stats-item strong {
    color: var(--text-primary);
}

.decisions-list {
    max-height: 600px;
    overflow-y: auto;
}

.decision-item {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.decision-item:hover {
    background-color: var(--bg-hover);
    border-color: var(--color-info);
}

.decision-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.decision-time {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-family: var(--font-mono);
}

.decision-confidence {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    font-family: var(--font-mono);
}

.confidence-high {
    background-color: var(--color-success);
    color: white;
}

.confidence-medium {
    background-color: var(--color-warning);
    color: white;
}

.confidence-low {
    background-color: var(--color-error);
    color: white;
}

.decision-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.decision-action {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.action-open-long {
    background-color: var(--color-success);
    color: white;
}

.action-open-short {
    background-color: var(--color-error);
    color: white;
}

.action-hold {
    background-color: var(--color-info);
    color: white;
}

.action-close-position {
    background-color: var(--color-warning);
    color: white;
}

.action-no-action {
    background-color: var(--text-muted);
    color: white;
}

.decision-symbol {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-primary);
}

.decision-reasoning {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

.decision-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.execution-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
    text-transform: uppercase;
}

.status-executed {
    background-color: var(--color-success);
    color: white;
}

.status-pending {
    background-color: var(--color-warning);
    color: white;
}

.status-failed {
    background-color: var(--color-error);
    color: white;
}

.status-skipped {
    background-color: var(--text-muted);
    color: white;
}

.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

.page-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

.decision-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.detail-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
}

.detail-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.detail-label {
    color: var(--text-secondary);
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
}

.reasoning-section {
    grid-column: 1 / -1;
}

.reasoning-text {
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
}

@media (max-width: 768px) {
    .filter-controls {
        grid-template-columns: 1fr;
    }
    
    .decision-detail-content {
        grid-template-columns: 1fr;
    }
    
    .decision-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .decision-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/ai_decisions.js') }}"></script>
{% endblock %}
