{% extends "base.html" %}

{% block title %}系统设置 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">系统设置</span>
{% endblock %}

{% block content %}
<div class="settings-container">
    <!-- 设置导航 -->
    <div class="settings-nav">
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="exchange">交易所配置</button>
            <button class="nav-tab" data-tab="trading">交易参数</button>
            <button class="nav-tab" data-tab="risk">风险控制</button>
            <button class="nav-tab" data-tab="symbols">交易对</button>
            <button class="nav-tab" data-tab="ai">AI设置</button>
        </div>
    </div>

    <!-- 交易所配置 -->
    <div class="tab-content active" id="exchange-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt"></i>
                    交易所配置
                </h3>
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator"></span>
                    <span class="status-text">未连接</span>
                </div>
            </div>
            <div class="card-body">
                <form id="exchangeForm" class="settings-form">
                    <div class="form-group">
                        <label>交易所选择</label>
                        <select id="exchangeName" class="form-control" required>
                            <option value="">请选择交易所</option>
                            <option value="okx">OKX</option>
                            <option value="binance">Binance</option>
                            <option value="huobi">Huobi</option>
                            <option value="bybit">Bybit</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>API Key</label>
                        <input type="text" id="apiKey" class="form-control" placeholder="输入API Key" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Secret Key</label>
                        <input type="password" id="secretKey" class="form-control" placeholder="输入Secret Key" required>
                    </div>
                    
                    <div class="form-group" id="passphraseGroup" style="display: none;">
                        <label>Passphrase</label>
                        <input type="password" id="passphrase" class="form-control" placeholder="输入Passphrase（OKX API密码短语）">
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="sandboxMode" checked>
                            <span class="checkmark"></span>
                            沙盒模式（测试环境）
                        </label>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="testConnection()">
                            <i class="fas fa-plug"></i>
                            测试连接
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存配置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 交易参数 -->
    <div class="tab-content" id="trading-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i>
                    交易参数配置
                </h3>
            </div>
            <div class="card-body">
                <form id="tradingForm" class="settings-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>最大杠杆倍数</label>
                            <input type="number" id="maxLeverage" class="form-control" min="1" max="100" value="10">
                            <small class="form-text">建议不超过20倍</small>
                        </div>
                        
                        <div class="form-group">
                            <label>最大仓位比例 (%)</label>
                            <input type="number" id="maxPositionRatio" class="form-control" min="10" max="100" value="50">
                            <small class="form-text">单次开仓最大资金比例</small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>开仓置信度阈值</label>
                            <input type="number" id="openingThreshold" class="form-control" min="0" max="100" value="70">
                            <small class="form-text">AI开仓决策的最低置信度</small>
                        </div>
                        
                        <div class="form-group">
                            <label>持仓置信度阈值</label>
                            <input type="number" id="positionThreshold" class="form-control" min="0" max="100" value="60">
                            <small class="form-text">AI持仓管理的最低置信度</small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>默认止损百分比 (%)</label>
                            <input type="number" id="stopLoss" class="form-control" min="1" max="50" step="0.1" value="5">
                            <small class="form-text">默认止损幅度</small>
                        </div>
                        
                        <div class="form-group">
                            <label>默认止盈百分比 (%)</label>
                            <input type="number" id="takeProfit" class="form-control" min="1" max="100" step="0.1" value="10">
                            <small class="form-text">默认止盈幅度</small>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetTradingParams()">
                            <i class="fas fa-undo"></i>
                            重置默认
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存参数
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 风险控制 -->
    <div class="tab-content" id="risk-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    风险控制参数
                </h3>
            </div>
            <div class="card-body">
                <form id="riskForm" class="settings-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>系统最大杠杆</label>
                            <input type="number" id="systemMaxLeverage" class="form-control" min="1" max="100" value="20">
                            <small class="form-text">系统允许的最大杠杆倍数</small>
                        </div>
                        
                        <div class="form-group">
                            <label>最大敞口比例 (%)</label>
                            <input type="number" id="maxExposureRatio" class="form-control" min="10" max="100" value="80">
                            <small class="form-text">总敞口与账户余额的最大比例</small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>最小余额阈值 (USDT)</label>
                            <input type="number" id="minBalanceThreshold" class="form-control" min="10" value="100">
                            <small class="form-text">账户最小保留余额</small>
                        </div>
                        
                        <div class="form-group">
                            <label>最大回撤限制 (%)</label>
                            <input type="number" id="maxDrawdown" class="form-control" min="1" max="50" value="20">
                            <small class="form-text">触发紧急停止的最大回撤</small>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetRiskParams()">
                            <i class="fas fa-undo"></i>
                            重置默认
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存参数
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 交易对选择 -->
    <div class="tab-content" id="symbols-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-coins"></i>
                    交易对选择
                </h3>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="loadAvailableSymbols()">
                        <i class="fas fa-sync-alt"></i>
                        刷新列表
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="symbols-selection">
                    <div class="search-box">
                        <input type="text" id="symbolSearch" class="form-control" placeholder="搜索交易对...">
                    </div>
                    
                    <div class="symbols-grid" id="symbolsGrid">
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载交易对列表中...</span>
                        </div>
                    </div>
                    
                    <div class="selected-symbols">
                        <h4>已选择的交易对 (<span id="selectedCount">0</span>)</h4>
                        <div class="selected-list" id="selectedList">
                            <div class="empty-state">暂未选择交易对</div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="clearAllSymbols()">
                            <i class="fas fa-trash"></i>
                            清空选择
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveSelectedSymbols()">
                            <i class="fas fa-save"></i>
                            保存选择
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI设置 -->
    <div class="tab-content" id="ai-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-brain"></i>
                    AI引擎设置
                </h3>
            </div>
            <div class="card-body">
                <form id="aiForm" class="settings-form">
                    <div class="form-group">
                        <label>DeepSeek API Key</label>
                        <input type="password" id="deepseekApiKey" class="form-control" placeholder="输入DeepSeek API Key">
                        <small class="form-text">用于AI决策分析</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>分析更新间隔 (秒)</label>
                            <input type="number" id="analysisInterval" class="form-control" min="30" max="3600" value="300">
                            <small class="form-text">AI分析的时间间隔</small>
                        </div>
                        
                        <div class="form-group">
                            <label>数据回看周期 (小时)</label>
                            <input type="number" id="lookbackPeriod" class="form-control" min="1" max="168" value="24">
                            <small class="form-text">技术分析的数据回看时间</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableOpeningEngine" checked>
                            <span class="checkmark"></span>
                            启用AI开仓引擎
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enablePositionEngine" checked>
                            <span class="checkmark"></span>
                            启用AI持仓引擎
                        </label>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="testAIConnection()">
                            <i class="fas fa-brain"></i>
                            测试AI连接
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', path='/css/settings.css') }}">
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/settings.js') }}"></script>
{% endblock %}
