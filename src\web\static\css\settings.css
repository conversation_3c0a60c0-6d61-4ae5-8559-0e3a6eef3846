/* DeepSeek量化交易系统 - 系统设置页面样式 */

/* 设置容器 */
.settings-container {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

/* 设置导航 */
.settings-nav {
    margin-bottom: var(--spacing-xl);
}

.nav-tabs {
    display: flex;
    gap: var(--spacing-sm);
    border-bottom: 1px solid var(--border-primary);
    margin-bottom: var(--spacing-lg);
}

.nav-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.nav-tab:hover {
    color: var(--text-primary);
    background-color: var(--bg-hover);
}

.nav-tab.active {
    color: var(--color-info);
    border-bottom-color: var(--color-info);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 表单样式 */
.settings-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.form-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--color-info);
}

/* 连接状态 */
.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-muted);
}

.status-indicator.connected {
    background-color: var(--color-success);
}

.status-indicator.disconnected {
    background-color: var(--color-error);
}

.status-text {
    color: var(--text-secondary);
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

/* 交易对选择 */
.symbols-selection {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-box {
    position: relative;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 40px;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
}

.search-box::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.symbols-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    max-height: 400px;
    overflow-y: auto;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
}

.symbol-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.symbol-item:hover {
    background-color: var(--bg-hover);
    border-color: var(--color-info);
}

.symbol-item.selected {
    background-color: rgba(31, 111, 235, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
}

.symbol-name {
    font-size: 0.875rem;
    font-weight: 500;
}

.symbol-check {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.symbol-item.selected .symbol-check {
    opacity: 1;
}

.selected-symbols {
    margin-top: var(--spacing-lg);
}

.selected-symbols h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    min-height: 60px;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
}

.selected-symbol-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--color-info);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.remove-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.remove-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-muted);
    text-align: center;
}

.empty-state i {
    font-size: 2rem;
    opacity: 0.5;
}

/* 加载状态 */
.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.loading-placeholder i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 300px;
    max-width: 500px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease;
}

.notification-info {
    border-left: 4px solid var(--color-info);
}

.notification-success {
    border-left: 4px solid var(--color-success);
}

.notification-warning {
    border-left: 4px solid var(--color-warning);
}

.notification-error {
    border-left: 4px solid var(--color-error);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    line-height: 1;
    padding: 0;
    margin-left: var(--spacing-md);
}

.notification-close:hover {
    color: var(--text-primary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-container {
        padding: var(--spacing-md);
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tab {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.75rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .symbols-grid {
        grid-template-columns: 1fr;
    }
    
    .notification {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}
