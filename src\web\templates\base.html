<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DeepSeek量化交易系统{% endblock %}</title>

    <!-- Ant Design主题CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/antd-theme.css') }}">

    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 额外样式 -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Ant Design布局容器 -->
    <div class="ant-layout">
        <!-- 顶部导航栏 -->
        <header class="trading-header">
            <div class="header-left">
                <a href="/" class="system-logo">
                    <i class="fas fa-robot"></i>
                    <span>DeepSeek量化交易系统</span>
                </a>
                <div class="system-status">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">系统状态检查中...</span>
                </div>
            </div>
            <div class="header-right">
                <div class="header-info">
                    <div class="current-time" id="currentTime"></div>
                    <div class="system-uptime" id="uptime">运行时间: --:--:--</div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="ant-layout" style="flex-direction: row;">
            <!-- 侧边导航栏 -->
            <aside class="trading-sider">
                <div class="sider-content">
                    <!-- 导航菜单 -->
                    <nav>
                        <ul class="nav-menu">
                            <li class="nav-item">
                                <a href="/" class="nav-link" data-page="dashboard">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>仪表板</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/positions" class="nav-link" data-page="positions">
                                    <i class="fas fa-chart-line"></i>
                                    <span>持仓管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/ai-decisions" class="nav-link" data-page="ai-decisions">
                                    <i class="fas fa-brain"></i>
                                    <span>AI决策</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/settings" class="nav-link" data-page="settings">
                                    <i class="fas fa-cog"></i>
                                    <span>系统设置</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/logs" class="nav-link" data-page="logs">
                                    <i class="fas fa-file-alt"></i>
                                    <span>系统日志</span>
                                </a>
                            </li>
                        </ul>
                    </nav>

                    <!-- 快速操作区 -->
                    <div class="quick-actions">
                        <div class="quick-actions-title">快速操作</div>
                        <button class="action-btn primary" id="startTradingBtn">
                            <i class="fas fa-play"></i>
                            <span>启动交易</span>
                        </button>
                        <button class="action-btn danger" id="stopTradingBtn">
                            <i class="fas fa-stop"></i>
                            <span>停止交易</span>
                        </button>
                        <button class="action-btn warning" id="emergencyStopBtn">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>紧急停止</span>
                        </button>
                    </div>
                </div>
            </aside>

            <!-- 主内容区 -->
            <main class="trading-content">
                <!-- 面包屑导航 -->
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </span>
                    {% block breadcrumb %}{% endblock %}
                </div>

                <!-- 页面内容 -->
                <div class="page-content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>

        <!-- 底部状态栏 -->
        <footer class="ant-layout-footer">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; gap: 24px;">
                    <span>
                        <i class="fas fa-server"></i>
                        服务器状态: <span id="serverStatus" class="text-success">正常</span>
                    </span>
                    <span>
                        <i class="fas fa-wifi"></i>
                        连接状态: <span id="connectionStatus" class="text-success">已连接</span>
                    </span>
                </div>
                <div style="display: flex; gap: 24px;">
                    <span>最后更新: <span id="lastUpdate">--</span></span>
                    <span>版本: v1.0.0</span>
                </div>
            </div>
        </footer>
    </div>



    <!-- 基础JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script src="{{ url_for('static', path='/js/antd-components.js') }}"></script>
    <script src="{{ url_for('static', path='/js/utils.js') }}"></script>
    
    <!-- 页面特定脚本 -->
    {% block extra_js %}{% endblock %}

    <script>
        // 基础页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化系统状态检查
            initSystemStatus();
            
            // 初始化导航高亮
            initNavigation();
            
            // 初始化时间显示
            initTimeDisplay();
            
            // 初始化快速操作按钮
            initQuickActions();
            

        });

        // 系统状态检查
        function initSystemStatus() {
            updateSystemStatus();
            setInterval(updateSystemStatus, 5000); // 每5秒更新一次
        }

        async function updateSystemStatus() {
            try {
                const response = await axios.get('/api/system/status');
                const status = response.data;

                const indicator = document.getElementById('statusIndicator');
                const text = document.getElementById('statusText');

                if (status.is_running) {
                    indicator.className = 'status-indicator running';
                    text.textContent = '系统运行中';
                } else {
                    indicator.className = 'status-indicator stopped';
                    text.textContent = '系统已停止';
                }
            } catch (error) {
                const indicator = document.getElementById('statusIndicator');
                const text = document.getElementById('statusText');
                indicator.className = 'status-indicator error';
                text.textContent = '连接异常';
            }
        }

        // 导航高亮
        function initNavigation() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        }

        // 时间显示
        function initTimeDisplay() {
            updateTime();
            setInterval(updateTime, 1000);
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 快速操作
        function initQuickActions() {
            document.getElementById('startTradingBtn').addEventListener('click', async () => {
                const confirmed = await antModal.confirm({
                    title: '启动交易系统',
                    content: '确定要启动自动交易吗？系统将开始执行AI决策和自动交易。',
                    type: 'confirm',
                    okText: '启动',
                    cancelText: '取消'
                });

                if (confirmed) {
                    antNotification.info('交易系统启动中...', '正在初始化交易引擎和AI模块');
                    // TODO: 实际的启动交易逻辑
                }
            });

            document.getElementById('stopTradingBtn').addEventListener('click', async () => {
                const confirmed = await antModal.confirm({
                    title: '停止交易系统',
                    content: '确定要停止自动交易吗？当前持仓将保持不变，但不会执行新的交易决策。',
                    type: 'warning',
                    okText: '停止',
                    cancelText: '取消'
                });

                if (confirmed) {
                    antNotification.warning('交易系统停止中...', '正在安全停止所有交易活动');
                    // TODO: 实际的停止交易逻辑
                }
            });

            document.getElementById('emergencyStopBtn').addEventListener('click', async () => {
                const confirmed = await antModal.confirm({
                    title: '紧急停止',
                    content: '确定要紧急停止所有交易吗？此操作将立即平仓所有持仓并停止系统！这是一个不可逆的操作。',
                    type: 'error',
                    okText: '紧急停止',
                    cancelText: '取消'
                });

                if (confirmed) {
                    antNotification.error('执行紧急停止...', '正在平仓所有持仓并停止系统');
                    // TODO: 实际的紧急停止逻辑
                }
            });
        }
    </script>
</body>
</html>
