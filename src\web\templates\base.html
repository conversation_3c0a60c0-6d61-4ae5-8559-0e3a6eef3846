<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DeepSeek量化交易系统{% endblock %}</title>
    
    <!-- 控制台风格CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/console.css') }}">
    
    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="console-body">
    <!-- 顶部导航栏 -->
    <header class="console-header">
        <div class="header-left">
            <h1 class="system-title">
                <i class="fas fa-robot"></i>
                DeepSeek量化交易系统
            </h1>
            <div class="system-status" id="systemStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span class="status-text" id="statusText">系统状态检查中...</span>
            </div>
        </div>
        <div class="header-right">
            <div class="current-time" id="currentTime"></div>
            <div class="system-info">
                <span id="uptime">运行时间: --:--:--</span>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="console-container">
        <!-- 侧边导航栏 -->
        <nav class="console-sidebar">
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/" class="nav-link" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/positions" class="nav-link" data-page="positions">
                        <i class="fas fa-chart-line"></i>
                        <span>持仓管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/ai-decisions" class="nav-link" data-page="ai-decisions">
                        <i class="fas fa-brain"></i>
                        <span>AI决策</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/logs" class="nav-link" data-page="logs">
                        <i class="fas fa-file-alt"></i>
                        <span>系统日志</span>
                    </a>
                </li>
            </ul>
            
            <!-- 快速操作区 -->
            <div class="quick-actions">
                <h3>快速操作</h3>
                <button class="action-btn" id="startTradingBtn">
                    <i class="fas fa-play"></i>
                    启动交易
                </button>
                <button class="action-btn danger" id="stopTradingBtn">
                    <i class="fas fa-stop"></i>
                    停止交易
                </button>
                <button class="action-btn warning" id="emergencyStopBtn">
                    <i class="fas fa-exclamation-triangle"></i>
                    紧急停止
                </button>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="console-main">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <span class="breadcrumb-item">
                    <i class="fas fa-home"></i>
                    首页
                </span>
                {% block breadcrumb %}{% endblock %}
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- 底部状态栏 -->
    <footer class="console-footer">
        <div class="footer-left">
            <span class="footer-item">
                <i class="fas fa-server"></i>
                服务器状态: <span id="serverStatus" class="status-ok">正常</span>
            </span>
            <span class="footer-item">
                <i class="fas fa-wifi"></i>
                连接状态: <span id="connectionStatus" class="status-ok">已连接</span>
            </span>
        </div>
        <div class="footer-right">
            <span class="footer-item">
                最后更新: <span id="lastUpdate">--</span>
            </span>
            <span class="footer-item">
                版本: v1.0.0
            </span>
        </div>
    </footer>

    <!-- 通知弹窗 -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- 确认对话框 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 id="modalTitle">确认操作</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="modalMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">取消</button>
                <button class="btn btn-primary" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <!-- 基础JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script src="{{ url_for('static', path='/js/utils.js') }}"></script>
    
    <!-- 页面特定脚本 -->
    {% block extra_js %}{% endblock %}

    <script>
        // 基础页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化系统状态检查
            initSystemStatus();
            
            // 初始化导航高亮
            initNavigation();
            
            // 初始化时间显示
            initTimeDisplay();
            
            // 初始化快速操作按钮
            initQuickActions();
            
            // 初始化模态框
            initModal();
        });

        // 系统状态检查
        function initSystemStatus() {
            updateSystemStatus();
            setInterval(updateSystemStatus, 5000); // 每5秒更新一次
        }

        async function updateSystemStatus() {
            try {
                const response = await axios.get('/api/system/status');
                const status = response.data;
                
                const indicator = document.getElementById('statusIndicator');
                const text = document.getElementById('statusText');
                
                if (status.is_running) {
                    indicator.className = 'status-indicator status-running';
                    text.textContent = '系统运行中';
                } else {
                    indicator.className = 'status-indicator status-stopped';
                    text.textContent = '系统已停止';
                }
            } catch (error) {
                const indicator = document.getElementById('statusIndicator');
                const text = document.getElementById('statusText');
                indicator.className = 'status-indicator status-error';
                text.textContent = '连接异常';
            }
        }

        // 导航高亮
        function initNavigation() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        }

        // 时间显示
        function initTimeDisplay() {
            updateTime();
            setInterval(updateTime, 1000);
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 快速操作
        function initQuickActions() {
            document.getElementById('startTradingBtn').addEventListener('click', () => {
                showConfirmModal('启动交易系统', '确定要启动自动交易吗？', () => {
                    // 启动交易逻辑
                    showNotification('交易系统启动中...', 'info');
                });
            });

            document.getElementById('stopTradingBtn').addEventListener('click', () => {
                showConfirmModal('停止交易系统', '确定要停止自动交易吗？', () => {
                    // 停止交易逻辑
                    showNotification('交易系统停止中...', 'warning');
                });
            });

            document.getElementById('emergencyStopBtn').addEventListener('click', () => {
                showConfirmModal('紧急停止', '确定要紧急停止所有交易吗？此操作将立即平仓所有持仓！', () => {
                    // 紧急停止逻辑
                    showNotification('执行紧急停止...', 'error');
                });
            });
        }

        // 模态框
        function initModal() {
            const overlay = document.getElementById('modalOverlay');
            const closeBtn = document.getElementById('modalClose');
            const cancelBtn = document.getElementById('modalCancel');

            closeBtn.addEventListener('click', hideModal);
            cancelBtn.addEventListener('click', hideModal);
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) hideModal();
            });
        }

        function showConfirmModal(title, message, onConfirm) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalMessage').textContent = message;
            document.getElementById('modalOverlay').style.display = 'flex';
            
            const confirmBtn = document.getElementById('modalConfirm');
            confirmBtn.onclick = () => {
                hideModal();
                onConfirm();
            };
        }

        function hideModal() {
            document.getElementById('modalOverlay').style.display = 'none';
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            container.appendChild(notification);
            
            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function getNotificationIcon(type) {
            const icons = {
                'info': 'info-circle',
                'success': 'check-circle',
                'warning': 'exclamation-triangle',
                'error': 'times-circle'
            };
            return icons[type] || 'info-circle';
        }
    </script>
</body>
</html>
