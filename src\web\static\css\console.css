/* DeepSeek量化交易系统 - 控制台风格样式 */

/* 基础变量 */
:root {
    /* 颜色主题 - 深色控制台风格 */
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --bg-hover: #30363d;
    
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #6e7681;
    
    --border-primary: #30363d;
    --border-secondary: #21262d;
    
    /* 状态颜色 */
    --color-success: #238636;
    --color-warning: #d29922;
    --color-error: #da3633;
    --color-info: #1f6feb;
    
    /* 特殊颜色 */
    --color-profit: #26a641;
    --color-loss: #f85149;
    --color-neutral: #8b949e;
    
    /* 字体 */
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 顶部导航栏 */
.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.system-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-title i {
    color: var(--color-info);
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.status-running {
    background-color: var(--color-success);
}

.status-indicator.status-stopped {
    background-color: var(--color-warning);
}

.status-indicator.status-error {
    background-color: var(--color-error);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.current-time {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-primary);
}

/* 主容器 */
.console-container {
    display: flex;
    min-height: calc(100vh - 120px);
}

/* 侧边栏 */
.console-sidebar {
    width: 280px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.nav-link:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.nav-link.active {
    background-color: var(--color-info);
    color: white;
}

.nav-link i {
    width: 16px;
    text-align: center;
}

/* 快速操作区 */
.quick-actions {
    border-top: 1px solid var(--border-primary);
    padding-top: var(--spacing-lg);
}

.quick-actions h3 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    background-color: var(--bg-hover);
    border-color: var(--text-muted);
}

.action-btn.danger {
    border-color: var(--color-error);
    color: var(--color-error);
}

.action-btn.danger:hover {
    background-color: var(--color-error);
    color: white;
}

.action-btn.warning {
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.action-btn.warning:hover {
    background-color: var(--color-warning);
    color: white;
}

/* 主内容区 */
.console-main {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

/* 面包屑 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: var(--spacing-sm);
    color: var(--text-muted);
}

/* 页面内容 */
.page-content {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    min-height: 600px;
}

/* 底部状态栏 */
.console-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: var(--font-mono);
}

.footer-left,
.footer-right {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-ok {
    color: var(--color-success);
}

.status-warning {
    color: var(--color-warning);
}

.status-error {
    color: var(--color-error);
}

/* 通用组件 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn:hover {
    background-color: var(--bg-hover);
    border-color: var(--text-muted);
}

.btn-primary {
    background-color: var(--color-info);
    border-color: var(--color-info);
    color: white;
}

.btn-primary:hover {
    background-color: #1a5fb4;
    border-color: #1a5fb4;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
}

.btn-danger {
    background-color: var(--color-error);
    border-color: var(--color-error);
    color: white;
}

.btn-danger:hover {
    background-color: #c93025;
    border-color: #c93025;
}

/* 卡片 */
.card {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-secondary);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* 表格 */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-secondary);
}

.table th {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background-color: var(--bg-hover);
}

/* 通知 */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-info {
    border-left: 4px solid var(--color-info);
}

.notification-success {
    border-left: 4px solid var(--color-success);
}

.notification-warning {
    border-left: 4px solid var(--color-warning);
}

.notification-error {
    border-left: 4px solid var(--color-error);
}

.notification-close {
    margin-left: auto;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.notification-close:hover {
    color: var(--text-primary);
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-dialog {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    font-size: 1.125rem;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    color: var(--text-secondary);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .console-container {
        flex-direction: column;
    }
    
    .console-sidebar {
        width: 100%;
        order: 2;
    }
    
    .console-main {
        order: 1;
    }
    
    .header-left,
    .header-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .footer-left,
    .footer-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}
