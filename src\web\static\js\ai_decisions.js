/**
 * DeepSeek量化交易系统 - AI决策页面JavaScript
 * 
 * 功能包括：
 * 1. AI引擎状态监控
 * 2. 决策历史查询和显示
 * 3. 筛选和搜索功能
 * 4. 实时数据更新
 */

class AIDecisionsManager {
    constructor() {
        this.decisions = [];
        this.filteredDecisions = [];
        this.currentFilters = {
            engine_type: 'all',
            symbol: 'all',
            action: 'all',
            confidence_min: 0,
            confidence_max: 100,
            time_range: '24h'
        };
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * 初始化AI决策管理器
     */
    init() {
        console.log('[AI Decisions] 初始化AI决策管理器');

        this.initFilters();
        this.initEventListeners();

        // 延迟加载数据，确保apiRequest函数已准备好
        this.waitForApiAndLoad();
        this.startAutoUpdate();

        console.log('[AI Decisions] AI决策管理器初始化完成');
    }

    /**
     * 等待API函数准备好后加载数据
     */
    async waitForApiAndLoad() {
        // 最多等待5秒
        const maxWaitTime = 5000;
        const checkInterval = 100;
        let waitedTime = 0;

        while (typeof apiRequest === 'undefined' && waitedTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitedTime += checkInterval;
        }

        if (typeof apiRequest !== 'undefined') {
            console.log('[AI Decisions] API函数已准备好，开始加载数据');
            this.loadAIDecisions();
        } else {
            console.warn('[AI Decisions] API函数等待超时，显示错误状态');
            this.showError('API函数加载超时，请刷新页面重试');
        }
    }

    /**
     * 初始化筛选器
     */
    initFilters() {
        // 引擎类型筛选
        const engineFilter = document.getElementById('engineFilter');
        if (engineFilter) {
            engineFilter.addEventListener('change', (e) => {
                this.currentFilters.engine_type = e.target.value;
                this.applyFilters();
            });
        }

        // 交易对筛选
        const symbolFilter = document.getElementById('symbolFilter');
        if (symbolFilter) {
            symbolFilter.addEventListener('change', (e) => {
                this.currentFilters.symbol = e.target.value;
                this.applyFilters();
            });
        }

        // 决策动作筛选
        const actionFilter = document.getElementById('actionFilter');
        if (actionFilter) {
            actionFilter.addEventListener('change', (e) => {
                this.currentFilters.action = e.target.value;
                this.applyFilters();
            });
        }

        // 置信度范围筛选
        const confidenceMinSlider = document.getElementById('confidenceMin');
        const confidenceMaxSlider = document.getElementById('confidenceMax');
        
        if (confidenceMinSlider && confidenceMaxSlider) {
            confidenceMinSlider.addEventListener('input', (e) => {
                this.currentFilters.confidence_min = parseInt(e.target.value);
                document.getElementById('confidenceMinValue').textContent = e.target.value;
                this.applyFilters();
            });
            
            confidenceMaxSlider.addEventListener('input', (e) => {
                this.currentFilters.confidence_max = parseInt(e.target.value);
                document.getElementById('confidenceMaxValue').textContent = e.target.value;
                this.applyFilters();
            });
        }

        // 时间范围筛选
        const timeRangeFilter = document.getElementById('timeRangeFilter');
        if (timeRangeFilter) {
            timeRangeFilter.addEventListener('change', (e) => {
                this.currentFilters.time_range = e.target.value;
                this.applyFilters();
            });
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadAIDecisions();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportDecisions();
            });
        }

        // 应用筛选按钮
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        // 重置筛选按钮
        const resetFiltersBtn = document.getElementById('resetFiltersBtn');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', () => {
                this.resetFilters();
            });
        }
    }

    /**
     * 加载AI决策数据
     */
    async loadAIDecisions() {
        try {
            console.log('[AI Decisions] 加载AI决策数据...');
            this.showLoading(true);

            // 确保apiRequest函数可用
            if (typeof apiRequest === 'undefined') {
                console.warn('[AI Decisions] apiRequest函数未定义，跳过加载AI决策数据');
                this.showError('API函数未就绪，请稍后重试');
                return;
            }

            const response = await apiRequest('/api/dashboard/ai-decisions', 'GET', this.currentFilters);
            
            if (response.success && response.data) {
                this.decisions = response.data.decisions || [];
                this.updateEngineStatus(response.data.engine_status || {});
                this.updateStatistics(response.data.statistics || {});
                this.populateSymbolFilter(response.data.available_symbols || []);
                this.applyFilters();
                
                console.log(`[AI Decisions] 加载了 ${this.decisions.length} 条决策记录`);
            } else {
                console.error('[AI Decisions] 加载决策数据失败:', response.message);
                this.showError('加载决策数据失败');
            }
        } catch (error) {
            console.error('[AI Decisions] 加载决策数据异常:', error);
            this.showError('加载决策数据异常');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新AI引擎状态
     */
    updateEngineStatus(engineStatus) {
        // 更新开仓引擎状态
        const openingStatus = document.getElementById('openingEngineStatus');
        const openingStats = document.getElementById('openingEngineStats');
        
        if (openingStatus && engineStatus.opening_engine) {
            const status = engineStatus.opening_engine;
            openingStatus.textContent = status.is_running ? '运行中' : '已停止';
            openingStatus.className = `engine-status ${status.is_running ? 'running' : 'stopped'}`;
            
            if (openingStats) {
                openingStats.innerHTML = `
                    <div class="stat-item">
                        <span>今日决策:</span>
                        <span>${status.today_decisions || '--'}</span>
                    </div>
                    <div class="stat-item">
                        <span>执行率:</span>
                        <span>${status.execution_rate || '--'}%</span>
                    </div>
                    <div class="stat-item">
                        <span>平均置信度:</span>
                        <span>${status.avg_confidence || '--'}%</span>
                    </div>
                `;
            }
        }

        // 更新持仓引擎状态
        const positionStatus = document.getElementById('positionEngineStatus');
        const positionStats = document.getElementById('positionEngineStats');
        
        if (positionStatus && engineStatus.position_engine) {
            const status = engineStatus.position_engine;
            positionStatus.textContent = status.is_running ? '运行中' : '已停止';
            positionStatus.className = `engine-status ${status.is_running ? 'running' : 'stopped'}`;
            
            if (positionStats) {
                positionStats.innerHTML = `
                    <div class="stat-item">
                        <span>今日决策:</span>
                        <span>${status.today_decisions || '--'}</span>
                    </div>
                    <div class="stat-item">
                        <span>执行率:</span>
                        <span>${status.execution_rate || '--'}%</span>
                    </div>
                    <div class="stat-item">
                        <span>平均置信度:</span>
                        <span>${status.avg_confidence || '--'}%</span>
                    </div>
                `;
            }
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics(statistics) {
        const totalElement = document.getElementById('totalDecisions');
        const executedElement = document.getElementById('executedDecisions');
        const successRateElement = document.getElementById('successRate');
        
        if (totalElement) {
            totalElement.textContent = statistics.total || '--';
        }
        
        if (executedElement) {
            executedElement.textContent = statistics.executed || '--';
        }
        
        if (successRateElement) {
            successRateElement.textContent = statistics.success_rate ? `${statistics.success_rate}%` : '--%';
        }
    }

    /**
     * 填充交易对筛选器
     */
    populateSymbolFilter(symbols) {
        const symbolFilter = document.getElementById('symbolFilter');
        if (!symbolFilter) return;
        
        // 保留当前选择
        const currentValue = symbolFilter.value;
        
        // 清空并重新填充
        symbolFilter.innerHTML = '<option value="all">全部</option>';
        symbols.forEach(symbol => {
            const option = document.createElement('option');
            option.value = symbol;
            option.textContent = symbol;
            symbolFilter.appendChild(option);
        });
        
        // 恢复选择
        if (symbols.includes(currentValue)) {
            symbolFilter.value = currentValue;
        }
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        this.filteredDecisions = this.decisions.filter(decision => {
            // 引擎类型筛选
            if (this.currentFilters.engine_type !== 'all' && 
                decision.engine_type !== this.currentFilters.engine_type) {
                return false;
            }
            
            // 交易对筛选
            if (this.currentFilters.symbol !== 'all' && 
                decision.symbol !== this.currentFilters.symbol) {
                return false;
            }
            
            // 决策动作筛选
            if (this.currentFilters.action !== 'all' && 
                decision.action !== this.currentFilters.action) {
                return false;
            }
            
            // 置信度范围筛选
            if (decision.confidence < this.currentFilters.confidence_min || 
                decision.confidence > this.currentFilters.confidence_max) {
                return false;
            }
            
            // 时间范围筛选
            const now = Date.now();
            const decisionTime = new Date(decision.timestamp).getTime();
            const timeRanges = {
                '1h': 60 * 60 * 1000,
                '6h': 6 * 60 * 60 * 1000,
                '24h': 24 * 60 * 60 * 1000,
                '7d': 7 * 24 * 60 * 60 * 1000,
                '30d': 30 * 24 * 60 * 60 * 1000
            };
            
            if (this.currentFilters.time_range !== 'all') {
                const timeLimit = timeRanges[this.currentFilters.time_range];
                if (timeLimit && (now - decisionTime) > timeLimit) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.renderDecisions();
        console.log(`[AI Decisions] 筛选后显示 ${this.filteredDecisions.length} 条记录`);
    }

    /**
     * 重置筛选器
     */
    resetFilters() {
        this.currentFilters = {
            engine_type: 'all',
            symbol: 'all',
            action: 'all',
            confidence_min: 0,
            confidence_max: 100,
            time_range: '24h'
        };
        
        // 重置UI
        document.getElementById('engineFilter').value = 'all';
        document.getElementById('symbolFilter').value = 'all';
        document.getElementById('actionFilter').value = 'all';
        document.getElementById('confidenceMin').value = 0;
        document.getElementById('confidenceMax').value = 100;
        document.getElementById('confidenceMinValue').textContent = '0';
        document.getElementById('confidenceMaxValue').textContent = '100';
        document.getElementById('timeRangeFilter').value = '24h';
        
        this.applyFilters();
        console.log('[AI Decisions] 筛选器已重置');
    }

    /**
     * 渲染决策列表
     */
    renderDecisions() {
        const container = document.getElementById('decisionsContainer');
        if (!container) return;
        
        if (this.filteredDecisions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <p>暂无符合条件的AI决策记录</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.filteredDecisions.map(decision => `
            <div class="decision-item" onclick="aiDecisionsManager.showDecisionDetail('${decision.id}')">
                <div class="decision-header">
                    <div class="decision-time">${this.formatTime(decision.timestamp)}</div>
                    <div class="decision-engine ${decision.engine_type}">${this.getEngineTypeName(decision.engine_type)}</div>
                    <div class="decision-confidence confidence-${this.getConfidenceLevel(decision.confidence)}">
                        ${decision.confidence}%
                    </div>
                </div>
                <div class="decision-content">
                    <div class="decision-symbol">${decision.symbol}</div>
                    <div class="decision-action action-${decision.action}">${this.getActionName(decision.action)}</div>
                    <div class="decision-status status-${decision.status}">${this.getStatusName(decision.status)}</div>
                </div>
                <div class="decision-reasoning">${decision.reasoning}</div>
            </div>
        `).join('');
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(isLoading) {
        const container = document.getElementById('decisionsContainer');
        if (!container) return;
        
        if (isLoading) {
            container.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载AI决策数据中...</span>
                </div>
            `;
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const container = document.getElementById('decisionsContainer');
        if (!container) return;
        
        container.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button onclick="aiDecisionsManager.loadAIDecisions()" class="retry-btn">重试</button>
            </div>
        `;
    }

    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        // 每30秒更新一次
        this.updateInterval = setInterval(() => {
            this.loadAIDecisions();
        }, 30000);
        
        console.log('[AI Decisions] 自动更新已启动');
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('[AI Decisions] 自动更新已停止');
        }
    }

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 获取引擎类型名称
     */
    getEngineTypeName(type) {
        const names = {
            'opening': '开仓引擎',
            'position': '持仓引擎'
        };
        return names[type] || type;
    }

    /**
     * 获取动作名称
     */
    getActionName(action) {
        const names = {
            'open_long': '开多',
            'open_short': '开空',
            'hold': '持有',
            'close': '平仓',
            'no_action': '无动作'
        };
        return names[action] || action;
    }

    /**
     * 获取状态名称
     */
    getStatusName(status) {
        const names = {
            'pending': '待执行',
            'executed': '已执行',
            'failed': '执行失败',
            'cancelled': '已取消'
        };
        return names[status] || status;
    }

    /**
     * 获取置信度等级
     */
    getConfidenceLevel(confidence) {
        if (confidence >= 80) return 'high';
        if (confidence >= 60) return 'medium';
        return 'low';
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopAutoUpdate();
        console.log('[AI Decisions] AI决策管理器已销毁');
    }
}

// 全局函数，供HTML调用
window.refreshDecisions = function() {
    if (window.aiDecisionsManager) {
        window.aiDecisionsManager.loadAIDecisions();
    }
};

window.exportDecisions = function() {
    if (window.aiDecisionsManager) {
        window.aiDecisionsManager.exportDecisions();
    }
};

window.applyFilters = function() {
    if (window.aiDecisionsManager) {
        window.aiDecisionsManager.applyFilters();
    }
};

window.resetFilters = function() {
    if (window.aiDecisionsManager) {
        window.aiDecisionsManager.resetFilters();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[AI Decisions] 页面加载完成，初始化AI决策管理器');
    window.aiDecisionsManager = new AIDecisionsManager();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.aiDecisionsManager) {
        window.aiDecisionsManager.destroy();
    }
});
