{% extends "base.html" %}

{% block title %}系统日志 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">系统日志</span>
{% endblock %}

{% block content %}
<div class="logs-container">
    <!-- 日志控制面板 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter"></i>
                日志筛选
            </h3>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
                <button class="btn btn-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash"></i>
                    清空
                </button>
                <button class="btn btn-primary" onclick="exportLogs()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="filter-controls">
                <div class="filter-group">
                    <label>日志级别:</label>
                    <select id="levelFilter" class="form-control">
                        <option value="">全部级别</option>
                        <option value="DEBUG">DEBUG</option>
                        <option value="INFO">INFO</option>
                        <option value="WARNING">WARNING</option>
                        <option value="ERROR">ERROR</option>
                        <option value="CRITICAL">CRITICAL</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>模块筛选:</label>
                    <select id="moduleFilter" class="form-control">
                        <option value="">全部模块</option>
                        <option value="trading_engine">交易引擎</option>
                        <option value="market_data_engine">市场数据</option>
                        <option value="ai_opening_engine">AI开仓</option>
                        <option value="ai_position_engine">AI持仓</option>
                        <option value="risk_manager">风险管理</option>
                        <option value="exchange_client">交易所客户端</option>
                        <option value="web_app">Web应用</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>关键词搜索:</label>
                    <input type="text" id="keywordFilter" class="form-control" placeholder="输入关键词...">
                </div>
                
                <div class="filter-group">
                    <label>时间范围:</label>
                    <select id="timeRangeFilter" class="form-control">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h" selected>最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="all">全部</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>显示数量:</label>
                    <select id="limitFilter" class="form-control">
                        <option value="100">100条</option>
                        <option value="500" selected>500条</option>
                        <option value="1000">1000条</option>
                        <option value="all">全部</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i>
                        应用筛选
                    </button>
                    <button class="btn btn-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志显示区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-terminal"></i>
                系统日志
            </h3>
            <div class="log-stats">
                <span class="stats-item">
                    总计: <strong id="totalLogs">--</strong>
                </span>
                <span class="stats-item">
                    错误: <strong id="errorLogs" class="text-danger">--</strong>
                </span>
                <span class="stats-item">
                    警告: <strong id="warningLogs" class="text-warning">--</strong>
                </span>
            </div>
            <div class="log-controls">
                <button class="btn btn-sm" onclick="toggleAutoScroll()" id="autoScrollBtn">
                    <i class="fas fa-arrow-down"></i>
                    自动滚动
                </button>
                <button class="btn btn-sm" onclick="toggleWordWrap()" id="wordWrapBtn">
                    <i class="fas fa-text-width"></i>
                    自动换行
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="log-viewer" id="logViewer">
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载日志数据中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal-overlay" id="logDetailModal" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-header">
                <h3 id="logDetailTitle">日志详情</h3>
                <button class="modal-close" onclick="hideLogDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="log-detail-content" id="logDetailContent">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideLogDetail()">关闭</button>
                <button class="btn btn-primary" onclick="copyLogData()">
                    <i class="fas fa-copy"></i>
                    复制日志
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 日志页面样式 */
.logs-container {
    padding: var(--spacing-lg);
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-control {
    padding: var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.log-stats {
    display: flex;
    gap: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stats-item strong {
    color: var(--text-primary);
}

.log-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.log-viewer {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    height: 600px;
    overflow-y: auto;
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.4;
}

.log-entry {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.log-entry:hover {
    background-color: var(--bg-hover);
}

.log-time {
    color: var(--text-muted);
    min-width: 80px;
    flex-shrink: 0;
}

.log-level {
    min-width: 60px;
    font-weight: 600;
    flex-shrink: 0;
}

.log-level.DEBUG {
    color: var(--text-muted);
}

.log-level.INFO {
    color: var(--color-info);
}

.log-level.WARNING {
    color: var(--color-warning);
}

.log-level.ERROR {
    color: var(--color-error);
}

.log-level.CRITICAL {
    color: var(--color-error);
    background-color: rgba(218, 54, 51, 0.2);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.log-module {
    color: var(--text-secondary);
    min-width: 120px;
    flex-shrink: 0;
}

.log-message {
    color: var(--text-primary);
    flex: 1;
    word-break: break-word;
}

.log-message.wrap {
    white-space: pre-wrap;
}

.log-message.nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.log-detail-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.detail-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
}

.detail-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.detail-item {
    display: flex;
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.detail-label {
    color: var(--text-secondary);
    min-width: 100px;
    flex-shrink: 0;
}

.detail-value {
    color: var(--text-primary);
    flex: 1;
    word-break: break-word;
}

.log-trace {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.75rem;
    color: var(--text-secondary);
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .filter-controls {
        grid-template-columns: 1fr;
    }
    
    .log-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .log-entry {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .log-time,
    .log-level,
    .log-module {
        min-width: auto;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/logs.js') }}"></script>
{% endblock %}
