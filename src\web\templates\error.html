{% extends "base.html" %}

{% block title %}系统错误 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">系统错误</span>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-card">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="error-content">
            <h1 class="error-title">系统错误</h1>
            <p class="error-message">
                {% if error_message %}
                    {{ error_message }}
                {% else %}
                    抱歉，系统遇到了一个错误。请稍后重试或联系管理员。
                {% endif %}
            </p>
            <div class="error-details">
                {% if error_details %}
                    <details>
                        <summary>错误详情</summary>
                        <pre class="error-trace">{{ error_details }}</pre>
                    </details>
                {% endif %}
            </div>
            <div class="error-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    返回首页
                </a>
                <button class="btn btn-secondary" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i>
                    返回上页
                </button>
                <button class="btn btn-secondary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    刷新页面
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: var(--spacing-xl);
}

.error-card {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    max-width: 600px;
    width: 100%;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.error-icon {
    font-size: 4rem;
    color: var(--color-error);
    margin-bottom: var(--spacing-lg);
}

.error-title {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.error-message {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.error-details {
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.error-details summary {
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-sm);
}

.error-details summary:hover {
    color: var(--text-primary);
}

.error-trace {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.75rem;
    color: var(--text-secondary);
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

.error-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .error-actions {
        flex-direction: column;
    }
    
    .error-card {
        padding: var(--spacing-lg);
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .error-icon {
        font-size: 3rem;
    }
}
</style>
{% endblock %}
