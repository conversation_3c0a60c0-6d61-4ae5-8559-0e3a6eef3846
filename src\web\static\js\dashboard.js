/**
 * DeepSeek量化交易系统 - 仪表板页面脚本
 * 
 * 负责仪表板页面的数据加载和交互功能，包括：
 * 1. 实时数据更新
 * 2. 统计卡片显示
 * 3. 持仓概览
 * 4. 风险监控
 * 5. AI决策历史
 * 6. 系统状态监控
 * 7. 实时日志显示
 */

class Dashboard {
    constructor() {
        this.updateInterval = 5000; // 5秒更新间隔
        this.logUpdateInterval = 2000; // 2秒日志更新间隔
        this.autoScroll = true;
        this.maxLogEntries = 100;
        
        // 定时器
        this.dataTimer = null;
        this.logTimer = null;
        
        // 数据缓存
        this.lastData = {};
        
        this.init();
    }

    /**
     * 初始化仪表板
     */
    init() {
        console.log('[Dashboard] 初始化仪表板');

        // 设置系统启动时间
        this.setSystemStartTime();

        // 立即加载数据
        this.loadAllData();

        // 启动定时更新
        this.startDataUpdates();
        this.startLogUpdates();

        // 绑定事件
        this.bindEvents();

        console.log('[Dashboard] 仪表板初始化完成');
    }

    /**
     * 设置系统启动时间
     */
    setSystemStartTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        const timeElement = document.getElementById('systemStartTime');
        if (timeElement) {
            timeElement.textContent = `[${timeString}]`;
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopUpdates();
            } else {
                this.startDataUpdates();
                this.startLogUpdates();
            }
        });

        // 窗口失焦时停止更新，获焦时恢复
        window.addEventListener('blur', () => this.stopUpdates());
        window.addEventListener('focus', () => {
            this.startDataUpdates();
            this.startLogUpdates();
        });
    }

    /**
     * 启动数据更新
     */
    startDataUpdates() {
        if (this.dataTimer) {
            clearInterval(this.dataTimer);
        }
        
        this.dataTimer = setInterval(() => {
            this.loadAllData();
        }, this.updateInterval);
    }

    /**
     * 启动日志更新
     */
    startLogUpdates() {
        if (this.logTimer) {
            clearInterval(this.logTimer);
        }
        
        this.logTimer = setInterval(() => {
            this.updateLogs();
        }, this.logUpdateInterval);
    }

    /**
     * 停止所有更新
     */
    stopUpdates() {
        if (this.dataTimer) {
            clearInterval(this.dataTimer);
            this.dataTimer = null;
        }
        
        if (this.logTimer) {
            clearInterval(this.logTimer);
            this.logTimer = null;
        }
    }

    /**
     * 加载所有数据
     */
    async loadAllData() {
        try {
            // 并行加载所有数据
            const [overview, account, positions, performance, aiDecisions, riskSummary] = await Promise.all([
                API.getDashboardOverview().catch(e => ({ error: e.message })),
                API.getAccountInfo().catch(e => ({ error: e.message })),
                API.getPositions().catch(e => ({ error: e.message })),
                API.getPerformance().catch(e => ({ error: e.message })),
                API.getAIDecisions(5).catch(e => ({ error: e.message })),
                API.getRiskSummary().catch(e => ({ error: e.message }))
            ]);

            // 更新各个部分
            this.updateStatsCards(overview, account, performance);
            this.updatePositionsSummary(positions);
            this.updateRiskMonitoring(riskSummary);
            this.updateAIDecisions(aiDecisions);
            this.updateSystemStatus(overview);

        } catch (error) {
            console.error('[Dashboard] 加载数据失败:', error);
            this.addLogEntry('ERROR', `数据加载失败: ${error.message}`);
        }
    }

    /**
     * 更新统计卡片
     */
    updateStatsCards(overview, account, performance) {
        try {
            // 总资产
            const totalBalance = account.total_balance_usdt || 0;
            document.getElementById('totalBalance').innerHTML = Formatter.currency(totalBalance);
            
            // 可用余额
            const availableBalance = account.available_balance_usdt || 0;
            document.getElementById('availableBalance').innerHTML = Formatter.currency(availableBalance);
            
            // 当日盈亏
            const dailyPnl = performance?.daily_stats?.daily_pnl || 0;
            const dailyReturn = performance?.daily_stats?.daily_return || 0;
            document.getElementById('dailyPnl').innerHTML = Formatter.currency(dailyPnl);
            document.getElementById('dailyPnlPercent').innerHTML = `
                <span class="change-value ${dailyReturn >= 0 ? 'positive' : 'negative'}">
                    ${Formatter.percentage(dailyReturn)}
                </span>
            `;
            
            // 持仓数量
            const totalPositions = overview?.positions_summary?.total_positions || 0;
            document.getElementById('totalPositions').innerHTML = totalPositions.toString();

        } catch (error) {
            console.error('[Dashboard] 更新统计卡片失败:', error);
        }
    }

    /**
     * 更新持仓概览
     */
    updatePositionsSummary(positionsData) {
        try {
            const container = document.getElementById('positionsSummary');
            
            if (positionsData.error) {
                container.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>加载持仓数据失败: ${positionsData.error}</span>
                    </div>
                `;
                return;
            }

            const positions = positionsData.positions || [];
            
            if (positions.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <span>暂无持仓</span>
                    </div>
                `;
                return;
            }

            const html = positions.map(position => {
                const side = Formatter.positionSide(position.side);
                const pnlClass = position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger';
                
                return `
                    <div class="position-item">
                        <div class="position-header">
                            <span class="position-symbol">${position.symbol}</span>
                            <span class="position-side ${side.class}">${side.text}</span>
                        </div>
                        <div class="position-details">
                            <div class="position-detail">
                                <span class="detail-label">数量:</span>
                                <span class="detail-value">${position.amount}</span>
                            </div>
                            <div class="position-detail">
                                <span class="detail-label">价格:</span>
                                <span class="detail-value">${Formatter.currency(position.current_price, '', 4)}</span>
                            </div>
                            <div class="position-detail">
                                <span class="detail-label">盈亏:</span>
                                <span class="detail-value ${pnlClass}">
                                    ${Formatter.currency(position.unrealized_pnl)}
                                    (${Formatter.percentage(position.unrealized_pnl_percentage)})
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;

        } catch (error) {
            console.error('[Dashboard] 更新持仓概览失败:', error);
        }
    }

    /**
     * 更新风险监控
     */
    updateRiskMonitoring(riskData) {
        try {
            if (riskData.error) {
                document.getElementById('riskIndicator').innerHTML = `
                    <span class="risk-level high">数据错误</span>
                `;
                return;
            }

            // 风险等级指示器
            const riskLevel = Formatter.riskLevel(riskData.risk_level);
            document.getElementById('riskIndicator').innerHTML = `
                <span class="risk-level ${riskData.risk_level}">${riskLevel.text}</span>
            `;

            // 风险指标
            document.getElementById('exposureRatio').textContent = Formatter.percentage(riskData.exposure_ratio);
            document.getElementById('marginUsage').textContent = Formatter.percentage(riskData.margin_usage);
            document.getElementById('emergencyMode').textContent = riskData.emergency_mode ? '是' : '否';
            document.getElementById('emergencyMode').className = `risk-value ${riskData.emergency_mode ? 'text-danger' : 'text-success'}`;

        } catch (error) {
            console.error('[Dashboard] 更新风险监控失败:', error);
        }
    }

    /**
     * 更新AI决策历史
     */
    updateAIDecisions(decisionsData) {
        try {
            const container = document.getElementById('aiDecisionsList');
            
            if (decisionsData.error) {
                container.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>加载AI决策失败: ${decisionsData.error}</span>
                    </div>
                `;
                return;
            }

            const decisions = decisionsData.decisions || [];
            
            if (decisions.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-brain"></i>
                        <span>暂无AI决策记录</span>
                    </div>
                `;
                return;
            }

            const html = decisions.map(decision => {
                const actionClass = {
                    'open_long': 'text-success',
                    'open_short': 'text-danger',
                    'hold': 'text-info',
                    'close_position': 'text-warning',
                    'no_action': 'text-muted'
                }[decision.action] || 'text-muted';

                return `
                    <div class="ai-decision-item">
                        <div class="decision-header">
                            <span class="decision-time">${Formatter.time(decision.timestamp, 'time')}</span>
                            <span class="decision-confidence">置信度: ${decision.confidence}%</span>
                        </div>
                        <div class="decision-content">
                            <span class="decision-action ${actionClass}">${decision.action}</span>
                            <span class="decision-symbol">${decision.symbol}</span>
                        </div>
                        <div class="decision-reasoning">
                            ${decision.reasoning.substring(0, 100)}...
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;

        } catch (error) {
            console.error('[Dashboard] 更新AI决策失败:', error);
        }
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(overview) {
        try {
            const systemStatus = overview.system_status || {};
            
            // 更新各个状态
            document.getElementById('tradingEngineStatus').textContent = systemStatus.is_running ? '运行中' : '已停止';
            document.getElementById('tradingEngineStatus').className = `status-value ${systemStatus.is_running ? 'text-success' : 'text-warning'}`;
            
            document.getElementById('openingEngineStatus').textContent = '正常';
            document.getElementById('openingEngineStatus').className = 'status-value text-success';
            
            document.getElementById('positionEngineStatus').textContent = '正常';
            document.getElementById('positionEngineStatus').className = 'status-value text-success';
            
            document.getElementById('marketDataStatus').textContent = '正常';
            document.getElementById('marketDataStatus').className = 'status-value text-success';
            
            // 运行时间
            const uptime = systemStatus.uptime || '00:00:00';
            document.getElementById('systemUptime').textContent = uptime;

        } catch (error) {
            console.error('[Dashboard] 更新系统状态失败:', error);
        }
    }

    /**
     * 更新日志
     */
    updateLogs() {
        // 模拟日志更新
        const logTypes = ['INFO', 'WARN', 'ERROR'];
        const messages = [
            '市场数据更新完成',
            'AI引擎分析中...',
            '风险检查通过',
            '持仓状态更新',
            '系统运行正常'
        ];

        if (Math.random() < 0.3) { // 30%概率添加新日志
            const type = logTypes[Math.floor(Math.random() * logTypes.length)];
            const message = messages[Math.floor(Math.random() * messages.length)];
            this.addLogEntry(type, message);
        }
    }

    /**
     * 添加日志条目
     */
    addLogEntry(level, message) {
        const logContainer = document.getElementById('consoleLog');
        const time = new Date().toLocaleTimeString('zh-CN');
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${level.toLowerCase()}`;
        logEntry.innerHTML = `
            <span class="log-time">[${time}]</span>
            <span class="log-level ${level}">${level}</span>
            <span class="log-message">${message}</span>
        `;
        
        logContainer.appendChild(logEntry);
        
        // 限制日志条目数量
        const entries = logContainer.querySelectorAll('.log-entry');
        if (entries.length > this.maxLogEntries) {
            entries[0].remove();
        }
        
        // 自动滚动到底部
        if (this.autoScroll) {
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }

    /**
     * 销毁仪表板
     */
    destroy() {
        this.stopUpdates();
        console.log('[Dashboard] 仪表板已销毁');
    }
}

// 全局函数
function refreshPositions() {
    dashboard.loadAllData();
    showNotification('正在刷新持仓数据...', 'info');
}

function refreshSystemStatus() {
    dashboard.loadAllData();
    showNotification('正在刷新系统状态...', 'info');
}

function clearLogs() {
    document.getElementById('consoleLog').innerHTML = '';
    showNotification('日志已清空', 'success');
}

function toggleAutoScroll() {
    dashboard.autoScroll = !dashboard.autoScroll;
    const btn = document.getElementById('autoScrollBtn');
    btn.innerHTML = dashboard.autoScroll ? 
        '<i class="fas fa-arrow-down"></i> 自动滚动' : 
        '<i class="fas fa-pause"></i> 手动滚动';
    
    showNotification(`自动滚动已${dashboard.autoScroll ? '开启' : '关闭'}`, 'info');
}

// 页面加载完成后初始化
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new Dashboard();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (dashboard) {
        dashboard.destroy();
    }
});
