/**
 * Ant Design风格组件和交互
 * 为DeepSeek量化交易系统提供现代化的用户界面交互
 */

// ==================== 通知组件 ====================
class AntNotification {
    constructor() {
        this.container = this.createContainer();
        this.notifications = new Map();
        this.counter = 0;
    }

    createContainer() {
        let container = document.getElementById('ant-notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'ant-notification-container';
            container.style.cssText = `
                position: fixed;
                top: 24px;
                right: 24px;
                z-index: 1010;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    show(config) {
        const id = ++this.counter;
        const notification = this.createNotification(id, config);
        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // 动画进入
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        });

        // 自动关闭
        if (config.duration !== 0) {
            setTimeout(() => {
                this.close(id);
            }, config.duration || 4500);
        }

        return id;
    }

    createNotification(id, config) {
        const notification = document.createElement('div');
        notification.className = 'ant-notification';
        notification.style.cssText = `
            background: var(--ant-bg-container);
            border: 1px solid var(--ant-border-color);
            border-radius: var(--ant-border-radius-base);
            box-shadow: var(--ant-box-shadow-base);
            padding: 16px 24px;
            margin-bottom: 16px;
            min-width: 384px;
            max-width: 384px;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
            pointer-events: auto;
            position: relative;
        `;

        const iconMap = {
            success: { icon: 'check-circle', color: 'var(--ant-success)' },
            error: { icon: 'times-circle', color: 'var(--ant-error)' },
            warning: { icon: 'exclamation-triangle', color: 'var(--ant-warning)' },
            info: { icon: 'info-circle', color: 'var(--ant-info)' }
        };

        const iconConfig = iconMap[config.type] || iconMap.info;

        notification.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <i class="fas fa-${iconConfig.icon}" style="color: ${iconConfig.color}; font-size: 16px; margin-top: 2px;"></i>
                <div style="flex: 1;">
                    <div style="color: var(--ant-text-primary); font-weight: 500; margin-bottom: 4px;">
                        ${config.message}
                    </div>
                    ${config.description ? `
                        <div style="color: var(--ant-text-secondary); font-size: 14px;">
                            ${config.description}
                        </div>
                    ` : ''}
                </div>
                <button onclick="antNotification.close(${id})" style="
                    background: none;
                    border: none;
                    color: var(--ant-text-tertiary);
                    cursor: pointer;
                    padding: 0;
                    font-size: 12px;
                    margin-top: 2px;
                ">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        return notification;
    }

    close(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    success(message, description) {
        return this.show({ type: 'success', message, description });
    }

    error(message, description) {
        return this.show({ type: 'error', message, description });
    }

    warning(message, description) {
        return this.show({ type: 'warning', message, description });
    }

    info(message, description) {
        return this.show({ type: 'info', message, description });
    }
}

// 创建全局通知实例
const antNotification = new AntNotification();

// ==================== 模态框组件 ====================
class AntModal {
    constructor() {
        this.modals = new Map();
        this.counter = 0;
    }

    confirm(config) {
        const id = ++this.counter;
        const modal = this.createModal(id, config);
        document.body.appendChild(modal);
        this.modals.set(id, modal);

        // 显示模态框
        requestAnimationFrame(() => {
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.style.opacity = '1';
                modal.querySelector('.ant-modal').style.transform = 'scale(1)';
            });
        });

        return new Promise((resolve) => {
            modal.dataset.resolve = resolve;
        });
    }

    createModal(id, config) {
        const modal = document.createElement('div');
        modal.className = 'ant-modal-mask';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--ant-bg-mask);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        `;

        const iconMap = {
            confirm: { icon: 'question-circle', color: 'var(--ant-primary-6)' },
            warning: { icon: 'exclamation-triangle', color: 'var(--ant-warning)' },
            error: { icon: 'times-circle', color: 'var(--ant-error)' },
            success: { icon: 'check-circle', color: 'var(--ant-success)' }
        };

        const iconConfig = iconMap[config.type] || iconMap.confirm;

        modal.innerHTML = `
            <div class="ant-modal" style="
                background: var(--ant-bg-container);
                border-radius: var(--ant-border-radius-base);
                box-shadow: var(--ant-box-shadow-base);
                min-width: 416px;
                max-width: 520px;
                transform: scale(0.9);
                transition: transform 0.2s;
            ">
                <div style="padding: 24px 24px 20px;">
                    <div style="display: flex; align-items: flex-start; gap: 16px;">
                        <i class="fas fa-${iconConfig.icon}" style="color: ${iconConfig.color}; font-size: 22px; margin-top: 2px;"></i>
                        <div style="flex: 1;">
                            <div style="color: var(--ant-text-primary); font-size: 16px; font-weight: 500; margin-bottom: 8px;">
                                ${config.title || '确认操作'}
                            </div>
                            <div style="color: var(--ant-text-secondary); line-height: 1.5715;">
                                ${config.content}
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 12px 24px 24px; text-align: right;">
                    <button onclick="antModal.close(${id}, false)" style="
                        background: var(--ant-bg-container);
                        border: 1px solid var(--ant-border-color);
                        border-radius: var(--ant-border-radius-base);
                        padding: 4px 15px;
                        margin-right: 8px;
                        color: var(--ant-text-primary);
                        cursor: pointer;
                        transition: all 0.2s;
                    " onmouseover="this.style.borderColor='var(--ant-primary-6)'; this.style.color='var(--ant-primary-6)'"
                       onmouseout="this.style.borderColor='var(--ant-border-color)'; this.style.color='var(--ant-text-primary)'">
                        ${config.cancelText || '取消'}
                    </button>
                    <button onclick="antModal.close(${id}, true)" style="
                        background: var(--ant-primary-6);
                        border: 1px solid var(--ant-primary-6);
                        border-radius: var(--ant-border-radius-base);
                        padding: 4px 15px;
                        color: #ffffff;
                        cursor: pointer;
                        transition: all 0.2s;
                    " onmouseover="this.style.background='var(--ant-primary-5)'; this.style.borderColor='var(--ant-primary-5)'"
                       onmouseout="this.style.background='var(--ant-primary-6)'; this.style.borderColor='var(--ant-primary-6)'">
                        ${config.okText || '确定'}
                    </button>
                </div>
            </div>
        `;

        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.close(id, false);
            }
        });

        return modal;
    }

    close(id, result) {
        const modal = this.modals.get(id);
        if (modal) {
            modal.style.opacity = '0';
            modal.querySelector('.ant-modal').style.transform = 'scale(0.9)';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                this.modals.delete(id);
                
                // 解析Promise
                if (modal.dataset.resolve) {
                    const resolve = new Function('return ' + modal.dataset.resolve)();
                    resolve(result);
                }
            }, 200);
        }
    }
}

// 创建全局模态框实例
const antModal = new AntModal();

// ==================== 工具函数 ====================

// 格式化数字
function formatNumber(num, decimals = 2) {
    if (num === null || num === undefined || isNaN(num)) return '--';
    return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

// 格式化百分比
function formatPercent(num, decimals = 2) {
    if (num === null || num === undefined || isNaN(num)) return '--';
    const formatted = (Number(num) * 100).toFixed(decimals);
    return `${formatted}%`;
}

// 格式化货币
function formatCurrency(num, currency = 'USDT', decimals = 2) {
    if (num === null || num === undefined || isNaN(num)) return '--';
    return `${formatNumber(num, decimals)} ${currency}`;
}

// 获取盈亏颜色类
function getPnlClass(value) {
    if (value > 0) return 'text-profit';
    if (value < 0) return 'text-loss';
    return 'text-neutral';
}

// ==================== 选项卡组件 ====================
class AntTabs {
    constructor(container) {
        this.container = container;
        this.tabs = container.querySelectorAll('.settings-tab');
        this.contents = container.querySelectorAll('.tab-content');
        this.init();
    }

    init() {
        this.tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = tab.dataset.tab;
                this.switchTab(targetTab);
            });
        });
    }

    switchTab(targetTab) {
        // 移除所有活动状态
        this.tabs.forEach(tab => tab.classList.remove('active'));
        this.contents.forEach(content => content.classList.remove('active'));

        // 激活目标选项卡
        const targetTabElement = document.querySelector(`[data-tab="${targetTab}"]`);
        const targetContent = document.getElementById(`${targetTab}-tab`);

        if (targetTabElement && targetContent) {
            targetTabElement.classList.add('active');
            targetContent.classList.add('active');

            // 添加动画效果
            targetContent.classList.add('fade-in');
            setTimeout(() => {
                targetContent.classList.remove('fade-in');
            }, 300);
        }
    }
}

// 初始化选项卡
document.addEventListener('DOMContentLoaded', function() {
    const tabContainer = document.querySelector('.page-content');
    if (tabContainer) {
        new AntTabs(tabContainer);
    }
});

// 导出到全局
window.antNotification = antNotification;
window.antModal = antModal;
window.formatNumber = formatNumber;
window.formatPercent = formatPercent;
window.formatCurrency = formatCurrency;
window.getPnlClass = getPnlClass;
window.AntTabs = AntTabs;
