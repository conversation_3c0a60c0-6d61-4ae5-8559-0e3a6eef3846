/* DeepSeek量化交易系统 - Ant Design风格主题 */

/* Ant Design设计语言变量系统 */
:root {
    /* 主色系 - 蓝色系 */
    --ant-primary-1: #e6f7ff;
    --ant-primary-2: #bae7ff;
    --ant-primary-3: #91d5ff;
    --ant-primary-4: #69c0ff;
    --ant-primary-5: #40a9ff;
    --ant-primary-6: #1890ff;  /* 主色 */
    --ant-primary-7: #096dd9;
    --ant-primary-8: #0050b3;
    --ant-primary-9: #003a8c;
    --ant-primary-10: #002766;

    /* 中性色系 - 深色主题 */
    --ant-gray-1: #ffffff;
    --ant-gray-2: #fafafa;
    --ant-gray-3: #f5f5f5;
    --ant-gray-4: #f0f0f0;
    --ant-gray-5: #d9d9d9;
    --ant-gray-6: #bfbfbf;
    --ant-gray-7: #8c8c8c;
    --ant-gray-8: #595959;
    --ant-gray-9: #434343;
    --ant-gray-10: #262626;
    --ant-gray-11: #1f1f1f;
    --ant-gray-12: #141414;
    --ant-gray-13: #000000;

    /* 功能色系 */
    --ant-success: #52c41a;
    --ant-warning: #faad14;
    --ant-error: #ff4d4f;
    --ant-info: #1890ff;

    /* 金融专用色系 */
    --ant-red-6: #f5222d;     /* 跌/亏损 */
    --ant-green-6: #52c41a;   /* 涨/盈利 */
    --ant-orange-6: #fa8c16;  /* 警告 */

    /* 深色主题背景色 */
    --ant-bg-base: var(--ant-gray-13);
    --ant-bg-container: var(--ant-gray-12);
    --ant-bg-elevated: var(--ant-gray-11);
    --ant-bg-layout: var(--ant-gray-13);
    --ant-bg-spotlight: var(--ant-gray-9);
    --ant-bg-mask: rgba(0, 0, 0, 0.45);

    /* 深色主题文字色 */
    --ant-text-primary: rgba(255, 255, 255, 0.85);
    --ant-text-secondary: rgba(255, 255, 255, 0.65);
    --ant-text-tertiary: rgba(255, 255, 255, 0.45);
    --ant-text-quaternary: rgba(255, 255, 255, 0.25);

    /* 深色主题边框色 */
    --ant-border-color: var(--ant-gray-9);
    --ant-border-color-split: var(--ant-gray-10);

    /* 字体系统 */
    --ant-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --ant-font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

    /* 字体大小 */
    --ant-font-size-base: 14px;
    --ant-font-size-lg: 16px;
    --ant-font-size-sm: 12px;
    --ant-font-size-xl: 20px;
    --ant-font-size-xxl: 24px;

    /* 行高 */
    --ant-line-height-base: 1.5715;

    /* 尺寸系统 */
    --ant-size-xs: 4px;
    --ant-size-sm: 8px;
    --ant-size-md: 16px;
    --ant-size-lg: 24px;
    --ant-size-xl: 32px;
    --ant-size-xxl: 48px;

    /* 圆角系统 */
    --ant-border-radius-base: 6px;
    --ant-border-radius-sm: 4px;
    --ant-border-radius-lg: 8px;

    /* 阴影系统 */
    --ant-box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    --ant-box-shadow-card: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);

    /* 动画时长 */
    --ant-motion-duration-slow: 0.3s;
    --ant-motion-duration-mid: 0.2s;
    --ant-motion-duration-fast: 0.1s;

    /* Z-index层级 */
    --ant-zindex-base: 0;
    --ant-zindex-affix: 10;
    --ant-zindex-dropdown: 1050;
    --ant-zindex-modal: 1000;
    --ant-zindex-notification: 1010;
    --ant-zindex-tooltip: 1060;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: var(--ant-font-size-base);
    line-height: var(--ant-line-height-base);
}

body {
    font-family: var(--ant-font-family);
    background-color: var(--ant-bg-layout);
    color: var(--ant-text-primary);
    line-height: var(--ant-line-height-base);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--ant-border-color);
    border-radius: 3px;
    transition: background var(--ant-motion-duration-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--ant-text-tertiary);
}

/* Firefox滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--ant-border-color) transparent;
}

/* 选择文本样式 */
::selection {
    background-color: var(--ant-primary-6);
    color: #ffffff;
}

/* 基础布局 */
.ant-layout {
    min-height: 100vh;
    background-color: var(--ant-bg-layout);
}

.ant-layout-header {
    background-color: var(--ant-bg-container);
    border-bottom: 1px solid var(--ant-border-color);
    padding: 0 var(--ant-size-lg);
    height: 64px;
    line-height: 64px;
    position: sticky;
    top: 0;
    z-index: var(--ant-zindex-affix);
}

.ant-layout-sider {
    background-color: var(--ant-bg-container);
    border-right: 1px solid var(--ant-border-color);
}

.ant-layout-content {
    background-color: var(--ant-bg-layout);
    padding: var(--ant-size-lg);
    min-height: calc(100vh - 64px);
}

.ant-layout-footer {
    background-color: var(--ant-bg-container);
    border-top: 1px solid var(--ant-border-color);
    padding: var(--ant-size-sm) var(--ant-size-lg);
    text-align: center;
    color: var(--ant-text-secondary);
}

/* 通用工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--ant-size-xs); }
.mb-2 { margin-bottom: var(--ant-size-sm); }
.mb-3 { margin-bottom: var(--ant-size-md); }
.mb-4 { margin-bottom: var(--ant-size-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--ant-size-xs); }
.mt-2 { margin-top: var(--ant-size-sm); }
.mt-3 { margin-top: var(--ant-size-md); }
.mt-4 { margin-top: var(--ant-size-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--ant-size-xs); }
.p-2 { padding: var(--ant-size-sm); }
.p-3 { padding: var(--ant-size-md); }
.p-4 { padding: var(--ant-size-lg); }

/* 状态颜色类 */
.text-success { color: var(--ant-success); }
.text-warning { color: var(--ant-warning); }
.text-error { color: var(--ant-error); }
.text-info { color: var(--ant-info); }
.text-profit { color: var(--ant-green-6); }
.text-loss { color: var(--ant-red-6); }

.bg-success { background-color: var(--ant-success); }
.bg-warning { background-color: var(--ant-warning); }
.bg-error { background-color: var(--ant-error); }
.bg-info { background-color: var(--ant-info); }

/* ==================== 顶部导航栏 ==================== */
.trading-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 0 var(--ant-size-lg);
    background-color: var(--ant-bg-container);
    border-bottom: 1px solid var(--ant-border-color);
    position: sticky;
    top: 0;
    z-index: var(--ant-zindex-affix);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--ant-size-lg);
}

.system-logo {
    display: flex;
    align-items: center;
    gap: var(--ant-size-sm);
    font-size: var(--ant-font-size-xl);
    font-weight: 600;
    color: var(--ant-text-primary);
    text-decoration: none;
}

.system-logo i {
    color: var(--ant-primary-6);
    font-size: var(--ant-font-size-xxl);
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--ant-size-sm);
    padding: var(--ant-size-xs) var(--ant-size-sm);
    background-color: var(--ant-bg-elevated);
    border-radius: var(--ant-border-radius-base);
    border: 1px solid var(--ant-border-color);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.running {
    background-color: var(--ant-success);
}

.status-indicator.stopped {
    background-color: var(--ant-gray-7);
}

.status-indicator.error {
    background-color: var(--ant-error);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: var(--ant-font-size-sm);
    color: var(--ant-text-secondary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--ant-size-lg);
}

.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--ant-size-xs);
}

.current-time {
    font-family: var(--ant-font-family-code);
    font-size: var(--ant-font-size-sm);
    color: var(--ant-text-primary);
}

.system-uptime {
    font-size: var(--ant-font-size-sm);
    color: var(--ant-text-tertiary);
}

/* ==================== 侧边导航栏 ==================== */
.trading-sider {
    width: 240px;
    background-color: var(--ant-bg-container);
    border-right: 1px solid var(--ant-border-color);
    height: calc(100vh - 64px);
    position: sticky;
    top: 64px;
    overflow-y: auto;
}

.sider-content {
    padding: var(--ant-size-lg) 0;
}

/* 导航菜单 */
.nav-menu {
    list-style: none;
    padding: 0 var(--ant-size-md);
}

.nav-item {
    margin-bottom: var(--ant-size-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--ant-size-sm);
    padding: var(--ant-size-sm) var(--ant-size-md);
    color: var(--ant-text-secondary);
    text-decoration: none;
    border-radius: var(--ant-border-radius-base);
    transition: all var(--ant-motion-duration-fast);
    font-size: var(--ant-font-size-base);
}

.nav-link:hover {
    background-color: var(--ant-bg-elevated);
    color: var(--ant-text-primary);
}

.nav-link.active {
    background-color: var(--ant-primary-1);
    color: var(--ant-primary-6);
    font-weight: 500;
}

.nav-link i {
    width: 16px;
    text-align: center;
    font-size: var(--ant-font-size-base);
}

/* 快速操作区 */
.quick-actions {
    margin-top: var(--ant-size-xl);
    padding: 0 var(--ant-size-md);
}

.quick-actions-title {
    font-size: var(--ant-font-size-sm);
    color: var(--ant-text-tertiary);
    margin-bottom: var(--ant-size-md);
    padding: 0 var(--ant-size-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ant-size-sm);
    padding: var(--ant-size-sm) var(--ant-size-md);
    margin-bottom: var(--ant-size-sm);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    background-color: var(--ant-bg-elevated);
    color: var(--ant-text-primary);
    font-size: var(--ant-font-size-sm);
    cursor: pointer;
    transition: all var(--ant-motion-duration-fast);
}

.action-btn:hover {
    border-color: var(--ant-primary-6);
    color: var(--ant-primary-6);
}

.action-btn.primary {
    background-color: var(--ant-primary-6);
    border-color: var(--ant-primary-6);
    color: #ffffff;
}

.action-btn.primary:hover {
    background-color: var(--ant-primary-5);
    border-color: var(--ant-primary-5);
}

.action-btn.danger {
    border-color: var(--ant-error);
    color: var(--ant-error);
}

.action-btn.danger:hover {
    background-color: var(--ant-error);
    color: #ffffff;
}

.action-btn.warning {
    border-color: var(--ant-warning);
    color: var(--ant-warning);
}

.action-btn.warning:hover {
    background-color: var(--ant-warning);
    color: #ffffff;
}

/* ==================== 主内容区域 ==================== */
.trading-content {
    flex: 1;
    padding: var(--ant-size-lg);
    background-color: var(--ant-bg-layout);
    min-height: calc(100vh - 64px);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--ant-size-sm);
    margin-bottom: var(--ant-size-lg);
    padding: var(--ant-size-sm) 0;
    color: var(--ant-text-secondary);
    font-size: var(--ant-font-size-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--ant-size-xs);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: var(--ant-size-sm);
    color: var(--ant-text-quaternary);
}

/* 页面标题 */
.page-header {
    margin-bottom: var(--ant-size-lg);
}

.page-title {
    font-size: var(--ant-font-size-xxl);
    font-weight: 600;
    color: var(--ant-text-primary);
    margin-bottom: var(--ant-size-sm);
}

.page-description {
    color: var(--ant-text-secondary);
    font-size: var(--ant-font-size-base);
}

/* ==================== 卡片组件 ==================== */
.ant-card {
    background-color: var(--ant-bg-container);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    box-shadow: var(--ant-box-shadow-card);
    margin-bottom: var(--ant-size-lg);
    overflow: hidden;
}

.ant-card-head {
    padding: var(--ant-size-md) var(--ant-size-lg);
    border-bottom: 1px solid var(--ant-border-color);
    background-color: var(--ant-bg-elevated);
}

.ant-card-head-title {
    font-size: var(--ant-font-size-lg);
    font-weight: 500;
    color: var(--ant-text-primary);
    margin: 0;
}

.ant-card-body {
    padding: var(--ant-size-lg);
}

.ant-card-small .ant-card-head {
    padding: var(--ant-size-sm) var(--ant-size-md);
}

.ant-card-small .ant-card-body {
    padding: var(--ant-size-md);
}

/* ==================== 统计卡片 ==================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--ant-size-lg);
    margin-bottom: var(--ant-size-xl);
}

.stat-card {
    background-color: var(--ant-bg-container);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    padding: var(--ant-size-lg);
    transition: all var(--ant-motion-duration-fast);
}

.stat-card:hover {
    border-color: var(--ant-primary-6);
    box-shadow: var(--ant-box-shadow-base);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ant-size-md);
}

.stat-title {
    font-size: var(--ant-font-size-base);
    color: var(--ant-text-secondary);
    margin: 0;
}

.stat-icon {
    font-size: var(--ant-font-size-xl);
    color: var(--ant-primary-6);
}

.stat-value {
    font-size: var(--ant-font-size-xxl);
    font-weight: 600;
    color: var(--ant-text-primary);
    margin-bottom: var(--ant-size-sm);
    font-family: var(--ant-font-family-code);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--ant-size-xs);
    font-size: var(--ant-font-size-sm);
}

.stat-change.positive {
    color: var(--ant-green-6);
}

.stat-change.negative {
    color: var(--ant-red-6);
}

.stat-change.neutral {
    color: var(--ant-text-tertiary);
}

/* ==================== 表格组件 ==================== */
.ant-table {
    background-color: var(--ant-bg-container);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    overflow: hidden;
}

.ant-table-thead > tr > th {
    background-color: var(--ant-bg-elevated);
    border-bottom: 1px solid var(--ant-border-color);
    padding: var(--ant-size-md) var(--ant-size-md);
    font-weight: 500;
    color: var(--ant-text-primary);
    text-align: left;
}

.ant-table-tbody > tr > td {
    padding: var(--ant-size-md) var(--ant-size-md);
    border-bottom: 1px solid var(--ant-border-color-split);
    color: var(--ant-text-primary);
}

.ant-table-tbody > tr:hover > td {
    background-color: var(--ant-bg-elevated);
}

.ant-table-tbody > tr:last-child > td {
    border-bottom: none;
}

/* 表格状态样式 */
.table-status {
    display: inline-flex;
    align-items: center;
    gap: var(--ant-size-xs);
    padding: var(--ant-size-xs) var(--ant-size-sm);
    border-radius: var(--ant-border-radius-sm);
    font-size: var(--ant-font-size-sm);
    font-weight: 500;
}

.table-status.success {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--ant-success);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.table-status.warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--ant-warning);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.table-status.error {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--ant-error);
    border: 1px solid rgba(255, 77, 79, 0.2);
}

/* ==================== 表单组件 ==================== */
.ant-form {
    max-width: 100%;
}

.ant-form-item {
    margin-bottom: var(--ant-size-lg);
}

.ant-form-item-label {
    display: block;
    margin-bottom: var(--ant-size-sm);
    color: var(--ant-text-primary);
    font-weight: 500;
    font-size: var(--ant-font-size-base);
}

.ant-form-item-required::before {
    content: '*';
    color: var(--ant-error);
    margin-right: var(--ant-size-xs);
}

.ant-input,
.ant-select,
.ant-textarea {
    width: 100%;
    padding: var(--ant-size-xs) var(--ant-size-sm);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    background-color: var(--ant-bg-container);
    color: var(--ant-text-primary);
    font-size: var(--ant-font-size-base);
    transition: all var(--ant-motion-duration-fast);
}

.ant-input:focus,
.ant-select:focus,
.ant-textarea:focus {
    border-color: var(--ant-primary-6);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.ant-input:hover,
.ant-select:hover,
.ant-textarea:hover {
    border-color: var(--ant-primary-5);
}

.ant-input::placeholder,
.ant-textarea::placeholder {
    color: var(--ant-text-quaternary);
}

.ant-select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

.ant-textarea {
    min-height: 80px;
    resize: vertical;
}

/* 按钮组件 */
.ant-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--ant-size-xs);
    padding: var(--ant-size-xs) var(--ant-size-md);
    border: 1px solid var(--ant-border-color);
    border-radius: var(--ant-border-radius-base);
    background-color: var(--ant-bg-container);
    color: var(--ant-text-primary);
    font-size: var(--ant-font-size-base);
    font-weight: 400;
    cursor: pointer;
    transition: all var(--ant-motion-duration-fast);
    text-decoration: none;
    user-select: none;
}

.ant-btn:hover {
    border-color: var(--ant-primary-6);
    color: var(--ant-primary-6);
}

.ant-btn:active {
    transform: translateY(1px);
}

.ant-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.ant-btn-primary {
    background-color: var(--ant-primary-6);
    border-color: var(--ant-primary-6);
    color: #ffffff;
}

.ant-btn-primary:hover {
    background-color: var(--ant-primary-5);
    border-color: var(--ant-primary-5);
    color: #ffffff;
}

.ant-btn-danger {
    background-color: var(--ant-error);
    border-color: var(--ant-error);
    color: #ffffff;
}

.ant-btn-danger:hover {
    background-color: #ff7875;
    border-color: #ff7875;
    color: #ffffff;
}

.ant-btn-success {
    background-color: var(--ant-success);
    border-color: var(--ant-success);
    color: #ffffff;
}

.ant-btn-success:hover {
    background-color: #73d13d;
    border-color: #73d13d;
    color: #ffffff;
}

.ant-btn-warning {
    background-color: var(--ant-warning);
    border-color: var(--ant-warning);
    color: #ffffff;
}

.ant-btn-warning:hover {
    background-color: #ffc53d;
    border-color: #ffc53d;
    color: #ffffff;
}

.ant-btn-lg {
    padding: var(--ant-size-sm) var(--ant-size-lg);
    font-size: var(--ant-font-size-lg);
}

.ant-btn-sm {
    padding: var(--ant-size-xs) var(--ant-size-sm);
    font-size: var(--ant-font-size-sm);
}

/* 开关组件 */
.ant-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
    background-color: var(--ant-gray-7);
    border-radius: 11px;
    cursor: pointer;
    transition: background-color var(--ant-motion-duration-fast);
}

.ant-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform var(--ant-motion-duration-fast);
}

.ant-switch.checked {
    background-color: var(--ant-primary-6);
}

.ant-switch.checked::after {
    transform: translateX(22px);
}

/* 分割线 */
.ant-divider {
    margin: var(--ant-size-lg) 0;
    border: none;
    border-top: 1px solid var(--ant-border-color-split);
}

.ant-divider-vertical {
    display: inline-block;
    width: 1px;
    height: 1em;
    background-color: var(--ant-border-color-split);
    margin: 0 var(--ant-size-sm);
    vertical-align: middle;
}

/* ==================== 选项卡组件 ==================== */
.settings-tab {
    display: inline-flex;
    align-items: center;
    gap: var(--ant-size-xs);
    padding: var(--ant-size-sm) var(--ant-size-md);
    border: none;
    background: none;
    color: var(--ant-text-secondary);
    font-size: var(--ant-font-size-base);
    cursor: pointer;
    transition: all var(--ant-motion-duration-fast);
    border-bottom: 2px solid transparent;
    position: relative;
}

.settings-tab:hover {
    color: var(--ant-primary-6);
}

.settings-tab.active {
    color: var(--ant-primary-6);
    border-bottom-color: var(--ant-primary-6);
}

.settings-tab i {
    font-size: var(--ant-font-size-base);
}

/* 选项卡内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .trading-sider {
        width: 200px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .trading-header {
        padding: 0 var(--ant-size-md);
    }

    .trading-content {
        padding: var(--ant-size-md);
    }

    .header-left .system-logo span {
        display: none;
    }

    .settings-tab {
        padding: var(--ant-size-xs) var(--ant-size-sm);
        font-size: var(--ant-font-size-sm);
    }

    .settings-tab span {
        display: none;
    }
}

@media (max-width: 480px) {
    .trading-sider {
        width: 60px;
    }

    .nav-link span {
        display: none;
    }

    .quick-actions-title {
        display: none;
    }

    .action-btn span {
        display: none;
    }

    .action-btn {
        padding: var(--ant-size-sm);
        justify-content: center;
    }
}

/* ==================== 加载状态 ==================== */
.loading {
    color: var(--ant-text-tertiary);
    font-style: italic;
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--ant-border-color);
    border-top: 2px solid var(--ant-primary-6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 特殊效果 ==================== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}
