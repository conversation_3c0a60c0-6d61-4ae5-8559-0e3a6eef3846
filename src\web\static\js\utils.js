/**
 * DeepSeek量化交易系统 - 工具函数库
 * 
 * 提供通用的工具函数，包括：
 * 1. 数据格式化
 * 2. API请求封装
 * 3. 时间处理
 * 4. 数值计算
 * 5. 状态管理
 */

// API基础配置
const API_BASE = '/api';
const REQUEST_TIMEOUT = 30000; // 30秒超时

// 配置axios默认设置
axios.defaults.timeout = REQUEST_TIMEOUT;
axios.defaults.headers.common['Content-Type'] = 'application/json';

// 请求拦截器
axios.interceptors.request.use(
    config => {
        // 添加请求时间戳
        config.metadata = { startTime: new Date() };
        console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
    },
    error => {
        console.error('[API请求错误]', error);
        return Promise.reject(error);
    }
);

// 响应拦截器
axios.interceptors.response.use(
    response => {
        // 计算请求耗时
        const endTime = new Date();
        const duration = endTime - response.config.metadata.startTime;
        console.log(`[API响应] ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);
        return response;
    },
    error => {
        console.error('[API响应错误]', error);
        
        // 统一错误处理
        if (error.response) {
            // 服务器返回错误状态码
            const { status, data } = error.response;
            showNotification(`请求失败: ${data.detail || data.message || '未知错误'} (${status})`, 'error');
        } else if (error.request) {
            // 网络错误
            showNotification('网络连接失败，请检查网络状态', 'error');
        } else {
            // 其他错误
            showNotification(`请求错误: ${error.message}`, 'error');
        }
        
        return Promise.reject(error);
    }
);

/**
 * 数据格式化工具
 */
const Formatter = {
    /**
     * 格式化数字为货币格式
     * @param {number} value - 数值
     * @param {string} currency - 货币符号
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的字符串
     */
    currency(value, currency = 'USDT', decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return `-- ${currency}`;
        }
        
        const formatted = Number(value).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
        
        return `${formatted} ${currency}`;
    },

    /**
     * 格式化百分比
     * @param {number} value - 数值
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的百分比
     */
    percentage(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '--%';
        }
        
        const formatted = Number(value).toFixed(decimals);
        const sign = value > 0 ? '+' : '';
        return `${sign}${formatted}%`;
    },

    /**
     * 格式化大数字（K, M, B）
     * @param {number} value - 数值
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的字符串
     */
    largeNumber(value, decimals = 1) {
        if (value === null || value === undefined || isNaN(value)) {
            return '--';
        }
        
        const absValue = Math.abs(value);
        const sign = value < 0 ? '-' : '';
        
        if (absValue >= 1e9) {
            return `${sign}${(absValue / 1e9).toFixed(decimals)}B`;
        } else if (absValue >= 1e6) {
            return `${sign}${(absValue / 1e6).toFixed(decimals)}M`;
        } else if (absValue >= 1e3) {
            return `${sign}${(absValue / 1e3).toFixed(decimals)}K`;
        } else {
            return `${sign}${absValue.toFixed(decimals)}`;
        }
    },

    /**
     * 格式化时间
     * @param {number|string|Date} timestamp - 时间戳或时间字符串
     * @param {string} format - 格式类型
     * @returns {string} 格式化后的时间
     */
    time(timestamp, format = 'datetime') {
        if (!timestamp) return '--';
        
        const date = new Date(typeof timestamp === 'number' ? timestamp * 1000 : timestamp);
        
        if (isNaN(date.getTime())) return '--';
        
        const options = {
            'date': { year: 'numeric', month: '2-digit', day: '2-digit' },
            'time': { hour: '2-digit', minute: '2-digit', second: '2-digit' },
            'datetime': { 
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit'
            },
            'relative': null // 相对时间
        };
        
        if (format === 'relative') {
            return this.relativeTime(date);
        }
        
        return date.toLocaleString('zh-CN', options[format]);
    },

    /**
     * 格式化相对时间
     * @param {Date} date - 日期对象
     * @returns {string} 相对时间字符串
     */
    relativeTime(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (seconds < 60) return `${seconds}秒前`;
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return this.time(date, 'datetime');
    },

    /**
     * 格式化持仓方向
     * @param {string} side - 持仓方向
     * @returns {object} 包含文本和样式类的对象
     */
    positionSide(side) {
        const sides = {
            'long': { text: '多头', class: 'text-success' },
            'short': { text: '空头', class: 'text-danger' },
            'buy': { text: '买入', class: 'text-success' },
            'sell': { text: '卖出', class: 'text-danger' }
        };
        
        return sides[side?.toLowerCase()] || { text: side || '--', class: 'text-muted' };
    },

    /**
     * 格式化风险等级
     * @param {string} level - 风险等级
     * @returns {object} 包含文本和样式类的对象
     */
    riskLevel(level) {
        const levels = {
            'low': { text: '低风险', class: 'text-success' },
            'medium': { text: '中等风险', class: 'text-warning' },
            'high': { text: '高风险', class: 'text-danger' },
            'critical': { text: '严重风险', class: 'text-danger blink' }
        };
        
        return levels[level?.toLowerCase()] || { text: level || '--', class: 'text-muted' };
    }
};

/**
 * API请求工具
 */
const API = {
    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        const response = await axios.get(`${API_BASE}/system/status`);
        return response.data;
    },

    /**
     * 获取仪表板概览
     */
    async getDashboardOverview() {
        const response = await axios.get(`${API_BASE}/dashboard/overview`);
        return response.data;
    },

    /**
     * 获取账户信息
     */
    async getAccountInfo() {
        const response = await axios.get(`${API_BASE}/dashboard/account`);
        return response.data;
    },

    /**
     * 获取持仓信息
     */
    async getPositions() {
        const response = await axios.get(`${API_BASE}/dashboard/positions`);
        return response.data;
    },

    /**
     * 获取交易表现
     */
    async getPerformance() {
        const response = await axios.get(`${API_BASE}/dashboard/performance`);
        return response.data;
    },

    /**
     * 获取AI决策历史
     */
    async getAIDecisions(limit = 10) {
        const response = await axios.get(`${API_BASE}/dashboard/ai-decisions/recent?limit=${limit}`);
        return response.data;
    },

    /**
     * 获取市场数据
     */
    async getMarketData(symbol, timeframe = '1h') {
        const response = await axios.get(`${API_BASE}/dashboard/market-data/${symbol}?timeframe=${timeframe}`);
        return response.data;
    },

    /**
     * 获取风险摘要
     */
    async getRiskSummary() {
        const response = await axios.get(`${API_BASE}/dashboard/risk-summary`);
        return response.data;
    },

    /**
     * 启动交易系统
     */
    async startTrading() {
        const response = await axios.post(`${API_BASE}/system/start`);
        return response.data;
    },

    /**
     * 停止交易系统
     */
    async stopTrading() {
        const response = await axios.post(`${API_BASE}/system/stop`);
        return response.data;
    },

    /**
     * 紧急停止
     */
    async emergencyStop() {
        const response = await axios.post(`${API_BASE}/system/emergency-stop`);
        return response.data;
    }
};

/**
 * 状态管理工具
 */
const StateManager = {
    // 本地状态存储
    state: new Map(),

    /**
     * 设置状态
     * @param {string} key - 状态键
     * @param {any} value - 状态值
     */
    set(key, value) {
        this.state.set(key, value);
        this.emit('stateChange', { key, value });
    },

    /**
     * 获取状态
     * @param {string} key - 状态键
     * @param {any} defaultValue - 默认值
     * @returns {any} 状态值
     */
    get(key, defaultValue = null) {
        return this.state.get(key) ?? defaultValue;
    },

    /**
     * 删除状态
     * @param {string} key - 状态键
     */
    delete(key) {
        const deleted = this.state.delete(key);
        if (deleted) {
            this.emit('stateChange', { key, value: undefined, deleted: true });
        }
        return deleted;
    },

    /**
     * 清空所有状态
     */
    clear() {
        this.state.clear();
        this.emit('stateChange', { cleared: true });
    },

    // 事件监听器
    listeners: new Map(),

    /**
     * 添加事件监听器
     * @param {string} event - 事件名
     * @param {function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    },

    /**
     * 移除事件监听器
     * @param {string} event - 事件名
     * @param {function} callback - 回调函数
     */
    off(event, callback) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    },

    /**
     * 触发事件
     * @param {string} event - 事件名
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件回调执行错误 [${event}]:`, error);
                }
            });
        }
    }
};

/**
 * 工具函数
 */
const Utils = {
    /**
     * 防抖函数
     * @param {function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     * @param {function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否成功
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            showNotification('已复制到剪贴板', 'success');
            return true;
        } catch (error) {
            console.error('复制失败:', error);
            showNotification('复制失败', 'error');
            return false;
        }
    }
};

/**
 * 通用API请求函数
 * @param {string} url - 请求URL
 * @param {string} method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {object} data - 请求数据
 * @param {object} options - 额外选项
 * @returns {Promise<object>} API响应
 */
async function apiRequest(url, method = 'GET', data = null, options = {}) {
    try {
        const config = {
            method: method.toUpperCase(),
            url: url.startsWith('/') ? url : `${API_BASE}/${url}`,
            ...options
        };

        // 添加请求数据
        if (data) {
            if (config.method === 'GET') {
                // GET请求将数据作为查询参数
                config.params = data;
            } else {
                // 其他请求将数据作为请求体
                config.data = data;
            }
        }

        const response = await axios(config);
        return response.data;

    } catch (error) {
        console.error(`[API请求失败] ${method} ${url}:`, error);

        // 返回统一的错误格式
        if (error.response?.data) {
            return error.response.data;
        } else {
            return {
                success: false,
                message: error.message || '请求失败',
                error: error
            };
        }
    }
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, warning, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showNotification(message, type = 'info', duration = 5000) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        min-width: 300px;
        max-width: 500px;
        background-color: var(--bg-secondary, #2a2a2a);
        border: 1px solid var(--border-primary, #444);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease;
        border-left: 4px solid ${getNotificationColor(type)};
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 添加关闭事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });

    // 自动关闭
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);

    console.log(`[通知] ${type.toUpperCase()}: ${message}`);
}

/**
 * 获取通知颜色
 * @param {string} type - 通知类型
 * @returns {string} 颜色值
 */
function getNotificationColor(type) {
    const colors = {
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    };
    return colors[type] || colors.info;
}

// 导出到全局作用域
window.Formatter = Formatter;
window.API = API;
window.StateManager = StateManager;
window.Utils = Utils;
window.apiRequest = apiRequest;
window.showNotification = showNotification;
