/**
 * DeepSeek量化交易系统 - 系统日志页面JavaScript
 * 
 * 功能包括：
 * 1. 日志数据加载和显示
 * 2. 日志筛选和搜索
 * 3. 实时日志更新
 * 4. 日志导出功能
 */

class LogsManager {
    constructor() {
        this.logs = [];
        this.filteredLogs = [];
        this.currentFilters = {
            level: '',
            module: '',
            keyword: '',
            time_range: '24h',
            limit: 500
        };
        this.autoScroll = true;
        this.wordWrap = false;
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * 初始化日志管理器
     */
    init() {
        console.log('[Logs] 初始化日志管理器');

        this.initFilters();
        this.initEventListeners();

        // 延迟加载数据，确保apiRequest函数已准备好
        this.waitForApiAndLoad();
        this.startAutoUpdate();

        console.log('[Logs] 日志管理器初始化完成');
    }

    /**
     * 等待API函数准备好后加载数据
     */
    async waitForApiAndLoad() {
        // 最多等待5秒
        const maxWaitTime = 5000;
        const checkInterval = 100;
        let waitedTime = 0;

        while (typeof apiRequest === 'undefined' && waitedTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitedTime += checkInterval;
        }

        if (typeof apiRequest !== 'undefined') {
            console.log('[Logs] API函数已准备好，开始加载数据');
            this.loadLogs();
        } else {
            console.warn('[Logs] API函数等待超时，显示错误状态');
            this.showError('API函数加载超时，请刷新页面重试');
        }
    }

    /**
     * 初始化筛选器
     */
    initFilters() {
        // 日志级别筛选
        const levelFilter = document.getElementById('levelFilter');
        if (levelFilter) {
            levelFilter.addEventListener('change', (e) => {
                this.currentFilters.level = e.target.value;
                this.applyFilters();
            });
        }

        // 模块筛选
        const moduleFilter = document.getElementById('moduleFilter');
        if (moduleFilter) {
            moduleFilter.addEventListener('change', (e) => {
                this.currentFilters.module = e.target.value;
                this.applyFilters();
            });
        }

        // 关键词搜索
        const keywordFilter = document.getElementById('keywordFilter');
        if (keywordFilter) {
            keywordFilter.addEventListener('input', (e) => {
                this.currentFilters.keyword = e.target.value;
                this.debounceApplyFilters();
            });
        }

        // 时间范围筛选
        const timeRangeFilter = document.getElementById('timeRangeFilter');
        if (timeRangeFilter) {
            timeRangeFilter.addEventListener('change', (e) => {
                this.currentFilters.time_range = e.target.value;
                this.applyFilters();
            });
        }

        // 显示数量筛选
        const limitFilter = document.getElementById('limitFilter');
        if (limitFilter) {
            limitFilter.addEventListener('change', (e) => {
                this.currentFilters.limit = e.target.value === 'all' ? null : parseInt(e.target.value);
                this.applyFilters();
            });
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadLogs();
            });
        }

        // 清空按钮
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearLogs();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportLogs();
            });
        }

        // 应用筛选按钮
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        // 重置筛选按钮
        const resetFiltersBtn = document.getElementById('resetFiltersBtn');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', () => {
                this.resetFilters();
            });
        }

        // 自动滚动按钮
        const autoScrollBtn = document.getElementById('autoScrollBtn');
        if (autoScrollBtn) {
            autoScrollBtn.addEventListener('click', () => {
                this.toggleAutoScroll();
            });
        }

        // 自动换行按钮
        const wordWrapBtn = document.getElementById('wordWrapBtn');
        if (wordWrapBtn) {
            wordWrapBtn.addEventListener('click', () => {
                this.toggleWordWrap();
            });
        }
    }

    /**
     * 加载日志数据
     */
    async loadLogs() {
        try {
            console.log('[Logs] 加载日志数据...');
            this.showLoading(true);

            // 确保apiRequest函数可用
            if (typeof apiRequest === 'undefined') {
                console.warn('[Logs] apiRequest函数未定义，跳过加载日志数据');
                this.showError('API函数未就绪，请稍后重试');
                return;
            }

            const response = await apiRequest('/api/system/logs', 'GET', this.currentFilters);
            
            if (response.success && response.data) {
                this.logs = response.data.logs || [];
                this.updateStatistics(response.data.statistics || {});
                this.applyFilters();
                
                console.log(`[Logs] 加载了 ${this.logs.length} 条日志记录`);
            } else {
                console.error('[Logs] 加载日志数据失败:', response.message);
                this.showError('加载日志数据失败');
            }
        } catch (error) {
            console.error('[Logs] 加载日志数据异常:', error);
            this.showError('加载日志数据异常');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics(statistics) {
        const totalElement = document.getElementById('totalLogs');
        const errorElement = document.getElementById('errorLogs');
        const warningElement = document.getElementById('warningLogs');
        
        if (totalElement) {
            totalElement.textContent = statistics.total || '--';
        }
        
        if (errorElement) {
            errorElement.textContent = statistics.error || '--';
        }
        
        if (warningElement) {
            warningElement.textContent = statistics.warning || '--';
        }
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        this.filteredLogs = this.logs.filter(log => {
            // 日志级别筛选
            if (this.currentFilters.level && log.level !== this.currentFilters.level) {
                return false;
            }
            
            // 模块筛选
            if (this.currentFilters.module && log.module !== this.currentFilters.module) {
                return false;
            }
            
            // 关键词搜索
            if (this.currentFilters.keyword) {
                const keyword = this.currentFilters.keyword.toLowerCase();
                const searchText = `${log.message} ${log.details || ''}`.toLowerCase();
                if (!searchText.includes(keyword)) {
                    return false;
                }
            }
            
            // 时间范围筛选
            const now = Date.now();
            const logTime = new Date(log.timestamp).getTime();
            const timeRanges = {
                '1h': 60 * 60 * 1000,
                '6h': 6 * 60 * 60 * 1000,
                '24h': 24 * 60 * 60 * 1000,
                '7d': 7 * 24 * 60 * 60 * 1000,
                'all': null
            };
            
            if (this.currentFilters.time_range !== 'all') {
                const timeLimit = timeRanges[this.currentFilters.time_range];
                if (timeLimit && (now - logTime) > timeLimit) {
                    return false;
                }
            }
            
            return true;
        });
        
        // 应用数量限制
        if (this.currentFilters.limit && this.filteredLogs.length > this.currentFilters.limit) {
            this.filteredLogs = this.filteredLogs.slice(-this.currentFilters.limit);
        }
        
        this.renderLogs();
        console.log(`[Logs] 筛选后显示 ${this.filteredLogs.length} 条日志`);
    }

    /**
     * 防抖应用筛选器
     */
    debounceApplyFilters() {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    /**
     * 重置筛选器
     */
    resetFilters() {
        this.currentFilters = {
            level: '',
            module: '',
            keyword: '',
            time_range: '24h',
            limit: 500
        };
        
        // 重置UI
        document.getElementById('levelFilter').value = '';
        document.getElementById('moduleFilter').value = '';
        document.getElementById('keywordFilter').value = '';
        document.getElementById('timeRangeFilter').value = '24h';
        document.getElementById('limitFilter').value = '500';
        
        this.applyFilters();
        console.log('[Logs] 筛选器已重置');
    }

    /**
     * 渲染日志列表
     */
    renderLogs() {
        const viewer = document.getElementById('logViewer');
        if (!viewer) return;
        
        if (this.filteredLogs.length === 0) {
            viewer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <p>暂无符合条件的日志记录</p>
                </div>
            `;
            return;
        }
        
        viewer.innerHTML = this.filteredLogs.map(log => `
            <div class="log-entry" onclick="logsManager.showLogDetail('${log.id}')">
                <div class="log-time">${this.formatTime(log.timestamp)}</div>
                <div class="log-level ${log.level}">${log.level}</div>
                <div class="log-module">${log.module}</div>
                <div class="log-message ${this.wordWrap ? 'wrap' : 'nowrap'}">${log.message}</div>
            </div>
        `).join('');
        
        // 自动滚动到底部
        if (this.autoScroll) {
            viewer.scrollTop = viewer.scrollHeight;
        }
    }

    /**
     * 显示日志详情
     */
    showLogDetail(logId) {
        const log = this.logs.find(l => l.id === logId);
        if (!log) return;
        
        const modal = document.getElementById('logDetailModal');
        const title = document.getElementById('logDetailTitle');
        const content = document.getElementById('logDetailContent');
        
        if (modal && title && content) {
            title.textContent = `日志详情 - ${log.level}`;
            
            content.innerHTML = `
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-item">
                        <span class="detail-label">时间:</span>
                        <span class="detail-value">${this.formatFullTime(log.timestamp)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">级别:</span>
                        <span class="detail-value log-level ${log.level}">${log.level}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">模块:</span>
                        <span class="detail-value">${log.module}</span>
                    </div>
                </div>
                <div class="detail-section">
                    <h4>日志消息</h4>
                    <div class="detail-value">${log.message}</div>
                </div>
                ${log.details ? `
                <div class="detail-section">
                    <h4>详细信息</h4>
                    <pre class="log-trace">${log.details}</pre>
                </div>
                ` : ''}
            `;
            
            modal.style.display = 'flex';
        }
    }

    /**
     * 隐藏日志详情
     */
    hideLogDetail() {
        const modal = document.getElementById('logDetailModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * 切换自动滚动
     */
    toggleAutoScroll() {
        this.autoScroll = !this.autoScroll;
        const btn = document.getElementById('autoScrollBtn');
        if (btn) {
            btn.classList.toggle('active', this.autoScroll);
            btn.title = this.autoScroll ? '关闭自动滚动' : '开启自动滚动';
        }
        console.log(`[Logs] 自动滚动: ${this.autoScroll ? '开启' : '关闭'}`);
    }

    /**
     * 切换自动换行
     */
    toggleWordWrap() {
        this.wordWrap = !this.wordWrap;
        const btn = document.getElementById('wordWrapBtn');
        if (btn) {
            btn.classList.toggle('active', this.wordWrap);
            btn.title = this.wordWrap ? '关闭自动换行' : '开启自动换行';
        }
        this.renderLogs(); // 重新渲染以应用换行设置
        console.log(`[Logs] 自动换行: ${this.wordWrap ? '开启' : '关闭'}`);
    }

    /**
     * 清空日志
     */
    async clearLogs() {
        if (!confirm('确定要清空所有日志吗？此操作不可恢复。')) {
            return;
        }

        try {
            console.log('[Logs] 清空日志...');

            // 确保apiRequest函数可用
            if (typeof apiRequest === 'undefined') {
                console.warn('[Logs] apiRequest函数未定义，无法清空日志');
                alert('API函数未就绪，请稍后重试');
                return;
            }

            const response = await apiRequest('/api/system/logs', 'DELETE');
            
            if (response.success) {
                this.logs = [];
                this.filteredLogs = [];
                this.renderLogs();
                this.updateStatistics({});
                console.log('[Logs] 日志已清空');
            } else {
                console.error('[Logs] 清空日志失败:', response.message);
            }
        } catch (error) {
            console.error('[Logs] 清空日志异常:', error);
        }
    }

    /**
     * 导出日志
     */
    exportLogs() {
        if (this.filteredLogs.length === 0) {
            alert('没有可导出的日志数据');
            return;
        }
        
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `logs_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        console.log(`[Logs] 导出了 ${this.filteredLogs.length} 条日志`);
    }

    /**
     * 生成CSV内容
     */
    generateCSV() {
        const headers = ['时间', '级别', '模块', '消息', '详情'];
        const rows = this.filteredLogs.map(log => [
            this.formatFullTime(log.timestamp),
            log.level,
            log.module,
            log.message.replace(/"/g, '""'),
            (log.details || '').replace(/"/g, '""')
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
            
        return '\uFEFF' + csvContent; // 添加BOM以支持中文
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(isLoading) {
        const viewer = document.getElementById('logViewer');
        if (!viewer) return;
        
        if (isLoading) {
            viewer.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载日志数据中...</span>
                </div>
            `;
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const viewer = document.getElementById('logViewer');
        if (!viewer) return;
        
        viewer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button onclick="logsManager.loadLogs()" class="retry-btn">重试</button>
            </div>
        `;
    }

    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        // 每10秒更新一次
        this.updateInterval = setInterval(() => {
            this.loadLogs();
        }, 10000);
        
        console.log('[Logs] 自动更新已启动');
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('[Logs] 自动更新已停止');
        }
    }

    /**
     * 格式化时间（简短）
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 格式化时间（完整）
     */
    formatFullTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopAutoUpdate();
        console.log('[Logs] 日志管理器已销毁');
    }
}

// 全局函数，供HTML调用
window.refreshLogs = function() {
    if (window.logsManager) {
        window.logsManager.loadLogs();
    }
};

window.clearLogs = function() {
    if (window.logsManager) {
        window.logsManager.clearLogs();
    }
};

window.exportLogs = function() {
    if (window.logsManager) {
        window.logsManager.exportLogs();
    }
};

window.applyFilters = function() {
    if (window.logsManager) {
        window.logsManager.applyFilters();
    }
};

window.resetFilters = function() {
    if (window.logsManager) {
        window.logsManager.resetFilters();
    }
};

window.toggleAutoScroll = function() {
    if (window.logsManager) {
        window.logsManager.toggleAutoScroll();
    }
};

window.toggleWordWrap = function() {
    if (window.logsManager) {
        window.logsManager.toggleWordWrap();
    }
};

window.hideLogDetail = function() {
    if (window.logsManager) {
        window.logsManager.hideLogDetail();
    }
};

window.copyLogData = function() {
    // 复制当前显示的日志详情到剪贴板
    const content = document.getElementById('logDetailContent');
    if (content) {
        navigator.clipboard.writeText(content.textContent).then(() => {
            console.log('[Logs] 日志数据已复制到剪贴板');
        });
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[Logs] 页面加载完成，初始化日志管理器');
    window.logsManager = new LogsManager();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.logsManager) {
        window.logsManager.destroy();
    }
});
