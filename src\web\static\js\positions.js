/**
 * DeepSeek量化交易系统 - 持仓管理页面脚本
 * 
 * 负责持仓管理页面的功能，包括：
 * 1. 持仓数据加载和显示
 * 2. 持仓详情查看
 * 3. 平仓操作
 * 4. 手动交易
 * 5. 实时数据更新
 */

class PositionsManager {
    constructor() {
        this.updateInterval = 5000; // 5秒更新间隔
        this.updateTimer = null;
        this.currentPositions = [];
        this.selectedPosition = null;
        this.selectedTradeSide = null;
        
        this.init();
    }

    /**
     * 初始化持仓管理器
     */
    init() {
        console.log('[Positions] 初始化持仓管理器');
        
        // 立即加载数据
        this.loadPositionsData();
        this.loadAvailableSymbols();
        
        // 启动定时更新
        this.startUpdates();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('[Positions] 持仓管理器初始化完成');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 订单类型变化
        document.getElementById('orderType').addEventListener('change', (e) => {
            const priceGroup = document.getElementById('priceGroup');
            if (e.target.value === 'limit') {
                priceGroup.style.display = 'block';
            } else {
                priceGroup.style.display = 'none';
            }
        });

        // 交易方向选择
        document.querySelectorAll('.trade-side-group .btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除其他按钮的active类
                document.querySelectorAll('.trade-side-group .btn').forEach(b => b.classList.remove('active'));
                
                // 添加active类到当前按钮
                btn.classList.add('active');
                this.selectedTradeSide = btn.dataset.side;
            });
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopUpdates();
            } else {
                this.startUpdates();
            }
        });
    }

    /**
     * 启动数据更新
     */
    startUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        this.updateTimer = setInterval(() => {
            this.loadPositionsData();
        }, this.updateInterval);
    }

    /**
     * 停止数据更新
     */
    stopUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * 加载持仓数据
     */
    async loadPositionsData() {
        try {
            const response = await API.getPositions();
            this.currentPositions = response.positions || [];
            
            this.updateOverviewCards(response);
            this.updatePositionsTable();
            
        } catch (error) {
            console.error('[Positions] 加载持仓数据失败:', error);
            this.showError('加载持仓数据失败');
        }
    }

    /**
     * 加载可用交易对
     */
    async loadAvailableSymbols() {
        try {
            // 这里应该从配置中获取选择的交易对
            const symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT'];
            
            const select = document.getElementById('tradeSymbol');
            select.innerHTML = '<option value="">选择交易对</option>';
            
            symbols.forEach(symbol => {
                const option = document.createElement('option');
                option.value = symbol;
                option.textContent = symbol;
                select.appendChild(option);
            });
            
        } catch (error) {
            console.error('[Positions] 加载交易对失败:', error);
        }
    }

    /**
     * 更新概览卡片
     */
    updateOverviewCards(data) {
        document.getElementById('totalPositions').textContent = data.total_count || 0;
        document.getElementById('totalExposure').textContent = Formatter.currency(data.total_exposure || 0);
        
        const totalPnl = data.total_unrealized_pnl || 0;
        const pnlElement = document.getElementById('totalPnl');
        pnlElement.textContent = Formatter.currency(totalPnl);
        pnlElement.className = `card-value ${totalPnl >= 0 ? 'pnl-positive' : 'pnl-negative'}`;
        
        const marginUsage = ((data.total_margin_used || 0) / (data.available_balance || 1)) * 100;
        document.getElementById('marginUsage').textContent = Formatter.percentage(marginUsage);
    }

    /**
     * 更新持仓表格
     */
    updatePositionsTable() {
        const tbody = document.getElementById('positionsTableBody');
        
        if (this.currentPositions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <span>暂无持仓</span>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        const rows = this.currentPositions.map(position => {
            const side = Formatter.positionSide(position.side);
            const pnlClass = position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
            
            return `
                <tr>
                    <td>
                        <strong>${position.symbol}</strong>
                    </td>
                    <td>
                        <span class="position-side ${position.side}">${side.text}</span>
                    </td>
                    <td>${position.amount}</td>
                    <td>${Formatter.currency(position.entry_price, '', 4)}</td>
                    <td>${Formatter.currency(position.current_price, '', 4)}</td>
                    <td>${position.leverage}x</td>
                    <td class="${pnlClass}">
                        ${Formatter.currency(position.unrealized_pnl)}
                    </td>
                    <td class="${pnlClass}">
                        ${Formatter.percentage(position.unrealized_pnl_percentage)}
                    </td>
                    <td>${Formatter.currency(position.margin_used)}</td>
                    <td>${Formatter.currency(position.liquidation_price, '', 4)}</td>
                    <td>
                        <div class="position-actions">
                            <button class="btn btn-xs btn-secondary" onclick="positionsManager.showPositionDetail('${position.symbol}')" title="详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-xs btn-warning" onclick="positionsManager.showClosePositionModal('${position.symbol}')" title="平仓">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        tbody.innerHTML = rows;
    }

    /**
     * 显示持仓详情
     */
    async showPositionDetail(symbol) {
        try {
            const response = await axios.get(`/api/trading/positions/${symbol}`);
            const position = response.data;
            
            this.selectedPosition = position;
            
            const content = `
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-item">
                        <span class="detail-label">交易对:</span>
                        <span class="detail-value">${position.symbol}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">方向:</span>
                        <span class="detail-value">${Formatter.positionSide(position.side).text}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">数量:</span>
                        <span class="detail-value">${position.amount}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">杠杆:</span>
                        <span class="detail-value">${position.leverage}x</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">开仓时间:</span>
                        <span class="detail-value">${Formatter.time(position.created_at)}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>价格信息</h4>
                    <div class="detail-item">
                        <span class="detail-label">开仓价格:</span>
                        <span class="detail-value">${Formatter.currency(position.entry_price, '', 4)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">当前价格:</span>
                        <span class="detail-value">${Formatter.currency(position.current_price, '', 4)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">强平价格:</span>
                        <span class="detail-value">${Formatter.currency(position.liquidation_price, '', 4)}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>盈亏信息</h4>
                    <div class="detail-item">
                        <span class="detail-label">未实现盈亏:</span>
                        <span class="detail-value ${position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative'}">
                            ${Formatter.currency(position.unrealized_pnl)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">盈亏比例:</span>
                        <span class="detail-value ${position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative'}">
                            ${Formatter.percentage(position.unrealized_pnl_percentage)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">使用保证金:</span>
                        <span class="detail-value">${Formatter.currency(position.margin_used)}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>利润回撤</h4>
                    <div class="detail-item">
                        <span class="detail-label">最高收益率:</span>
                        <span class="detail-value">${Formatter.percentage(position.profit_drawdown?.max_profit_rate || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">当前收益率:</span>
                        <span class="detail-value">${Formatter.percentage(position.profit_drawdown?.current_profit_rate || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">回撤幅度:</span>
                        <span class="detail-value">${Formatter.percentage(position.profit_drawdown?.drawdown_amount || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">回撤比例:</span>
                        <span class="detail-value">${Formatter.percentage(position.profit_drawdown?.drawdown_percentage || 0)}</span>
                    </div>
                </div>
            `;
            
            document.getElementById('positionDetailContent').innerHTML = content;
            document.getElementById('positionDetailTitle').textContent = `持仓详情 - ${symbol}`;
            document.getElementById('positionDetailModal').style.display = 'flex';
            
        } catch (error) {
            console.error('[Positions] 获取持仓详情失败:', error);
            showNotification('获取持仓详情失败', 'error');
        }
    }

    /**
     * 显示平仓模态框
     */
    showClosePositionModal(symbol) {
        const position = this.currentPositions.find(p => p.symbol === symbol);
        if (!position) {
            showNotification('未找到持仓信息', 'error');
            return;
        }

        this.selectedPosition = position;
        
        document.getElementById('closeSymbol').textContent = symbol;
        document.getElementById('closeCurrentAmount').textContent = position.amount;
        document.getElementById('closeAmount').value = '';
        document.getElementById('estimatedPnl').textContent = Formatter.currency(position.unrealized_pnl);
        document.getElementById('estimatedPnl').className = `estimated-pnl ${position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative'}`;
        
        document.getElementById('closePositionModal').style.display = 'flex';
    }

    /**
     * 显示手动交易模态框
     */
    showManualTradeModal() {
        // 重置表单
        document.getElementById('tradeSymbol').value = '';
        document.getElementById('tradeAmount').value = '';
        document.getElementById('tradePrice').value = '';
        document.getElementById('tradeLeverage').value = '10';
        document.getElementById('orderType').value = 'market';
        document.getElementById('priceGroup').style.display = 'none';
        
        // 重置交易方向选择
        document.querySelectorAll('.trade-side-group .btn').forEach(btn => btn.classList.remove('active'));
        this.selectedTradeSide = null;
        
        document.getElementById('manualTradeModal').style.display = 'flex';
    }

    /**
     * 确认平仓
     */
    async confirmClosePosition() {
        if (!this.selectedPosition) {
            showNotification('未选择持仓', 'error');
            return;
        }

        try {
            const amount = document.getElementById('closeAmount').value;
            
            const response = await axios.post('/api/trading/positions/close', {
                symbol: this.selectedPosition.symbol,
                amount: amount ? parseFloat(amount) : null
            });

            if (response.data.success) {
                showNotification('平仓操作已提交', 'success');
                this.hideClosePositionModal();
                this.loadPositionsData(); // 刷新数据
            } else {
                showNotification('平仓操作失败', 'error');
            }

        } catch (error) {
            console.error('[Positions] 平仓操作失败:', error);
            showNotification('平仓操作失败', 'error');
        }
    }

    /**
     * 提交手动交易
     */
    async submitManualTrade() {
        try {
            const symbol = document.getElementById('tradeSymbol').value;
            const amount = document.getElementById('tradeAmount').value;
            const orderType = document.getElementById('orderType').value;
            const price = document.getElementById('tradePrice').value;
            const leverage = document.getElementById('tradeLeverage').value;

            if (!symbol || !amount || !this.selectedTradeSide) {
                showNotification('请填写完整的交易信息', 'warning');
                return;
            }

            if (orderType === 'limit' && !price) {
                showNotification('限价单必须填写价格', 'warning');
                return;
            }

            const orderData = {
                symbol,
                side: this.selectedTradeSide,
                amount: parseFloat(amount),
                order_type: orderType,
                leverage: parseInt(leverage)
            };

            if (orderType === 'limit') {
                orderData.price = parseFloat(price);
            }

            const response = await axios.post('/api/trading/orders/manual', orderData);

            if (response.data.success) {
                showNotification('订单已提交', 'success');
                this.hideManualTradeModal();
                this.loadPositionsData(); // 刷新数据
            } else {
                showNotification('订单提交失败', 'error');
            }

        } catch (error) {
            console.error('[Positions] 手动交易失败:', error);
            showNotification('订单提交失败', 'error');
        }
    }

    /**
     * 隐藏模态框
     */
    hidePositionDetail() {
        document.getElementById('positionDetailModal').style.display = 'none';
        this.selectedPosition = null;
    }

    hideClosePositionModal() {
        document.getElementById('closePositionModal').style.display = 'none';
        this.selectedPosition = null;
    }

    hideManualTradeModal() {
        document.getElementById('manualTradeModal').style.display = 'none';
        this.selectedTradeSide = null;
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const tbody = document.getElementById('positionsTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="11" class="text-center">
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>${message}</span>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopUpdates();
        console.log('[Positions] 持仓管理器已销毁');
    }
}

// 全局函数
function refreshPositions() {
    positionsManager.loadPositionsData();
    showNotification('正在刷新持仓数据...', 'info');
}

function showCloseAllModal() {
    showConfirmModal('全部平仓', '确定要平仓所有持仓吗？此操作不可撤销！', () => {
        // 这里实现全部平仓逻辑
        showNotification('全部平仓功能开发中...', 'info');
    });
}

function setCloseAmountAll() {
    if (positionsManager.selectedPosition) {
        document.getElementById('closeAmount').value = positionsManager.selectedPosition.amount;
    }
}

function hidePositionDetail() {
    positionsManager.hidePositionDetail();
}

function hideClosePositionModal() {
    positionsManager.hideClosePositionModal();
}

function hideManualTradeModal() {
    positionsManager.hideManualTradeModal();
}

function showManualTradeModal() {
    positionsManager.showManualTradeModal();
}

function confirmClosePosition() {
    positionsManager.confirmClosePosition();
}

function submitManualTrade() {
    positionsManager.submitManualTrade();
}

// 页面加载完成后初始化
let positionsManager;
document.addEventListener('DOMContentLoaded', function() {
    positionsManager = new PositionsManager();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (positionsManager) {
        positionsManager.destroy();
    }
});
