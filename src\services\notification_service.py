#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知服务

此模块负责系统通知的管理，包括：
1. 多种通知渠道支持（邮件、短信、Webhook等）
2. 通知模板管理
3. 通知优先级和频率控制
4. 通知历史记录
5. 通知状态跟踪
"""

import time
import json
import smtplib
import requests
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from email.header import Header
from enum import Enum

from src.utils.logger import get_logger
from src.utils.exceptions import NotificationError

logger = get_logger(__name__)


class NotificationType(Enum):
    """通知类型枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    TRADE = "trade"
    SYSTEM = "system"


class NotificationChannel(Enum):
    """通知渠道枚举"""
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    CONSOLE = "console"


@dataclass
class NotificationConfig:
    """通知配置"""
    enabled: bool = True
    channels: List[str] = None
    email_config: Dict[str, Any] = None
    sms_config: Dict[str, Any] = None
    webhook_config: Dict[str, Any] = None
    rate_limit: Dict[str, int] = None  # 频率限制
    
    def __post_init__(self):
        if self.channels is None:
            self.channels = [NotificationChannel.CONSOLE.value]
        if self.rate_limit is None:
            self.rate_limit = {
                "info": 60,      # 信息类通知：每分钟最多1次
                "warning": 30,   # 警告类通知：每30秒最多1次
                "error": 10,     # 错误类通知：每10秒最多1次
                "critical": 0    # 严重通知：无限制
            }


@dataclass
class Notification:
    """通知对象"""
    id: str
    type: NotificationType
    title: str
    message: str
    timestamp: float
    channels: List[NotificationChannel]
    priority: int = 1  # 1-5，5为最高优先级
    data: Optional[Dict[str, Any]] = None
    sent_channels: List[str] = None
    failed_channels: List[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.sent_channels is None:
            self.sent_channels = []
        if self.failed_channels is None:
            self.failed_channels = []


class NotificationService:
    """通知服务类"""
    
    def __init__(self, config: Optional[NotificationConfig] = None):
        """初始化通知服务
        
        Args:
            config: 通知配置
        """
        self.config = config or NotificationConfig()
        
        # 通知历史
        self.notification_history: List[Notification] = []
        self.max_history_size = 1000
        
        # 频率限制跟踪
        self.rate_limit_tracker: Dict[str, float] = {}
        
        # 通知模板
        self.templates = self._load_templates()
        
        logger.info("通知服务初始化完成")
    
    def send_notification(self, 
                         notification_type: NotificationType,
                         title: str,
                         message: str,
                         channels: Optional[List[NotificationChannel]] = None,
                         priority: int = 1,
                         data: Optional[Dict[str, Any]] = None) -> str:
        """发送通知
        
        Args:
            notification_type: 通知类型
            title: 通知标题
            message: 通知内容
            channels: 发送渠道
            priority: 优先级
            data: 附加数据
        
        Returns:
            str: 通知ID
        """
        try:
            # 检查服务是否启用
            if not self.config.enabled:
                logger.debug("通知服务已禁用，跳过发送")
                return ""
            
            # 检查频率限制
            if not self._check_rate_limit(notification_type):
                logger.debug(f"通知频率限制，跳过发送: {notification_type.value}")
                return ""
            
            # 创建通知对象
            notification_id = self._generate_notification_id()
            notification = Notification(
                id=notification_id,
                type=notification_type,
                title=title,
                message=message,
                timestamp=time.time(),
                channels=channels or [NotificationChannel.CONSOLE],
                priority=priority,
                data=data
            )
            
            # 发送到各个渠道
            self._send_to_channels(notification)
            
            # 记录到历史
            self._add_to_history(notification)
            
            logger.info(f"通知发送完成: {notification_id} - {title}")
            return notification_id
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            raise NotificationError(f"发送通知失败: {e}")
    
    def send_trade_notification(self, action: str, symbol: str, amount: float, price: float, 
                               result: str = "success", **kwargs) -> str:
        """发送交易通知
        
        Args:
            action: 交易动作
            symbol: 交易对
            amount: 数量
            price: 价格
            result: 结果
            **kwargs: 其他参数
        
        Returns:
            str: 通知ID
        """
        title = f"交易通知 - {action.upper()}"
        message = f"交易对: {symbol}\n数量: {amount}\n价格: {price}\n结果: {result}"
        
        data = {
            "action": action,
            "symbol": symbol,
            "amount": amount,
            "price": price,
            "result": result,
            **kwargs
        }
        
        notification_type = NotificationType.TRADE
        priority = 3 if result == "success" else 4
        
        return self.send_notification(
            notification_type=notification_type,
            title=title,
            message=message,
            priority=priority,
            data=data
        )
    
    def send_system_alert(self, alert_type: str, message: str, severity: str = "warning", 
                         **kwargs) -> str:
        """发送系统告警
        
        Args:
            alert_type: 告警类型
            message: 告警消息
            severity: 严重程度
            **kwargs: 其他参数
        
        Returns:
            str: 通知ID
        """
        severity_map = {
            "info": NotificationType.INFO,
            "warning": NotificationType.WARNING,
            "error": NotificationType.ERROR,
            "critical": NotificationType.CRITICAL
        }
        
        notification_type = severity_map.get(severity, NotificationType.WARNING)
        title = f"系统告警 - {alert_type}"
        
        priority_map = {
            "info": 1,
            "warning": 2,
            "error": 4,
            "critical": 5
        }
        priority = priority_map.get(severity, 2)
        
        data = {
            "alert_type": alert_type,
            "severity": severity,
            **kwargs
        }
        
        return self.send_notification(
            notification_type=notification_type,
            title=title,
            message=message,
            priority=priority,
            data=data
        )
    
    def send_ai_decision_notification(self, engine_type: str, symbol: str, action: str, 
                                    confidence: int, reasoning: str) -> str:
        """发送AI决策通知
        
        Args:
            engine_type: 引擎类型
            symbol: 交易对
            action: 决策动作
            confidence: 置信度
            reasoning: 决策理由
        
        Returns:
            str: 通知ID
        """
        title = f"AI决策 - {engine_type}"
        message = f"交易对: {symbol}\n动作: {action}\n置信度: {confidence}%\n理由: {reasoning}"
        
        data = {
            "engine_type": engine_type,
            "symbol": symbol,
            "action": action,
            "confidence": confidence,
            "reasoning": reasoning
        }
        
        return self.send_notification(
            notification_type=NotificationType.INFO,
            title=title,
            message=message,
            priority=2,
            data=data
        )
    
    def get_notification_history(self, limit: int = 50, 
                               notification_type: Optional[NotificationType] = None) -> List[Dict[str, Any]]:
        """获取通知历史
        
        Args:
            limit: 返回数量限制
            notification_type: 通知类型筛选
        
        Returns:
            List[Dict[str, Any]]: 通知历史列表
        """
        history = self.notification_history
        
        # 类型筛选
        if notification_type:
            history = [n for n in history if n.type == notification_type]
        
        # 按时间倒序排列
        history = sorted(history, key=lambda x: x.timestamp, reverse=True)
        
        # 限制数量
        history = history[:limit]
        
        # 转换为字典格式
        return [asdict(notification) for notification in history]
    
    def update_config(self, config: NotificationConfig):
        """更新通知配置
        
        Args:
            config: 新的通知配置
        """
        self.config = config
        logger.info("通知配置已更新")
    
    def _send_to_channels(self, notification: Notification):
        """发送到各个渠道"""
        for channel in notification.channels:
            try:
                if channel == NotificationChannel.CONSOLE:
                    self._send_console(notification)
                elif channel == NotificationChannel.EMAIL:
                    self._send_email(notification)
                elif channel == NotificationChannel.SMS:
                    self._send_sms(notification)
                elif channel == NotificationChannel.WEBHOOK:
                    self._send_webhook(notification)
                
                notification.sent_channels.append(channel.value)
                
            except Exception as e:
                logger.error(f"发送到渠道 {channel.value} 失败: {e}")
                notification.failed_channels.append(channel.value)
    
    def _send_console(self, notification: Notification):
        """发送到控制台"""
        level_map = {
            NotificationType.INFO: "INFO",
            NotificationType.WARNING: "WARNING",
            NotificationType.ERROR: "ERROR",
            NotificationType.CRITICAL: "CRITICAL",
            NotificationType.TRADE: "TRADE",
            NotificationType.SYSTEM: "SYSTEM"
        }
        
        level = level_map.get(notification.type, "INFO")
        logger.info(f"[{level}] {notification.title}: {notification.message}")
    
    def _send_email(self, notification: Notification):
        """发送邮件通知"""
        if not self.config.email_config:
            raise NotificationError("邮件配置未设置")
        
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.config.email_config['from']
            msg['To'] = self.config.email_config['to']
            msg['Subject'] = Header(notification.title, 'utf-8')
            
            # 邮件内容
            body = self._format_email_body(notification)
            msg.attach(MimeText(body, 'html', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(
                self.config.email_config['smtp_server'],
                self.config.email_config['smtp_port']
            )
            server.starttls()
            server.login(
                self.config.email_config['username'],
                self.config.email_config['password']
            )
            server.send_message(msg)
            server.quit()
            
            logger.debug(f"邮件通知发送成功: {notification.id}")
            
        except Exception as e:
            raise NotificationError(f"邮件发送失败: {e}")
    
    def _send_sms(self, notification: Notification):
        """发送短信通知"""
        if not self.config.sms_config:
            raise NotificationError("短信配置未设置")
        
        # 这里应该集成实际的短信服务提供商API
        # 目前只是模拟
        logger.debug(f"短信通知发送成功: {notification.id}")
    
    def _send_webhook(self, notification: Notification):
        """发送Webhook通知"""
        if not self.config.webhook_config:
            raise NotificationError("Webhook配置未设置")
        
        try:
            payload = {
                "id": notification.id,
                "type": notification.type.value,
                "title": notification.title,
                "message": notification.message,
                "timestamp": notification.timestamp,
                "priority": notification.priority,
                "data": notification.data
            }
            
            response = requests.post(
                self.config.webhook_config['url'],
                json=payload,
                headers=self.config.webhook_config.get('headers', {}),
                timeout=10
            )
            response.raise_for_status()
            
            logger.debug(f"Webhook通知发送成功: {notification.id}")
            
        except Exception as e:
            raise NotificationError(f"Webhook发送失败: {e}")
    
    def _check_rate_limit(self, notification_type: NotificationType) -> bool:
        """检查频率限制"""
        type_key = notification_type.value
        rate_limit = self.config.rate_limit.get(type_key, 0)
        
        if rate_limit == 0:  # 无限制
            return True
        
        last_sent = self.rate_limit_tracker.get(type_key, 0)
        current_time = time.time()
        
        if current_time - last_sent >= rate_limit:
            self.rate_limit_tracker[type_key] = current_time
            return True
        
        return False
    
    def _generate_notification_id(self) -> str:
        """生成通知ID"""
        return f"notif_{int(time.time() * 1000)}_{len(self.notification_history)}"
    
    def _add_to_history(self, notification: Notification):
        """添加到历史记录"""
        self.notification_history.append(notification)
        
        # 限制历史记录大小
        if len(self.notification_history) > self.max_history_size:
            self.notification_history = self.notification_history[-self.max_history_size:]
    
    def _format_email_body(self, notification: Notification) -> str:
        """格式化邮件内容"""
        template = self.templates.get('email', {}).get(notification.type.value)
        if template:
            return template.format(
                title=notification.title,
                message=notification.message,
                timestamp=datetime.fromtimestamp(notification.timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                data=notification.data or {}
            )
        
        # 默认模板
        message_html = notification.message.replace('\n', '<br>')
        data_html = f'<p><strong>附加数据:</strong> {notification.data}</p>' if notification.data else ''

        return f"""
        <html>
        <body>
            <h2>{notification.title}</h2>
            <p><strong>时间:</strong> {datetime.fromtimestamp(notification.timestamp).strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>类型:</strong> {notification.type.value}</p>
            <p><strong>优先级:</strong> {notification.priority}</p>
            <p><strong>内容:</strong></p>
            <p>{message_html}</p>
            {data_html}
        </body>
        </html>
        """
    
    def _load_templates(self) -> Dict[str, Any]:
        """加载通知模板"""
        # 这里可以从文件或数据库加载模板
        # 目前返回默认模板
        return {
            "email": {
                "trade": """
                <html>
                <body>
                    <h2>交易通知</h2>
                    <p><strong>时间:</strong> {timestamp}</p>
                    <p><strong>交易对:</strong> {data[symbol]}</p>
                    <p><strong>动作:</strong> {data[action]}</p>
                    <p><strong>数量:</strong> {data[amount]}</p>
                    <p><strong>价格:</strong> {data[price]}</p>
                    <p><strong>结果:</strong> {data[result]}</p>
                </body>
                </html>
                """,
                "system": """
                <html>
                <body>
                    <h2>系统告警</h2>
                    <p><strong>时间:</strong> {timestamp}</p>
                    <p><strong>告警类型:</strong> {data[alert_type]}</p>
                    <p><strong>严重程度:</strong> {data[severity]}</p>
                    <p><strong>详情:</strong> {message}</p>
                </body>
                </html>
                """
            }
        }
