{% extends "base.html" %}

{% block title %}持仓管理 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">持仓管理</span>
{% endblock %}

{% block content %}
<div class="positions-container">
    <!-- 持仓概览卡片 -->
    <div class="overview-cards">
        <div class="overview-card">
            <div class="card-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <div class="card-content">
                <h3>总持仓数</h3>
                <div class="card-value" id="totalPositions">--</div>
            </div>
        </div>
        
        <div class="overview-card">
            <div class="card-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="card-content">
                <h3>总敞口</h3>
                <div class="card-value" id="totalExposure">--</div>
            </div>
        </div>
        
        <div class="overview-card">
            <div class="card-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div class="card-content">
                <h3>总盈亏</h3>
                <div class="card-value" id="totalPnl">--</div>
            </div>
        </div>
        
        <div class="overview-card">
            <div class="card-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="card-content">
                <h3>保证金使用率</h3>
                <div class="card-value" id="marginUsage">--</div>
            </div>
        </div>
    </div>

    <!-- 持仓列表 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list"></i>
                当前持仓
            </h3>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshPositions()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
                <button class="btn btn-danger" onclick="showCloseAllModal()">
                    <i class="fas fa-times-circle"></i>
                    全部平仓
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table positions-table" id="positionsTable">
                    <thead>
                        <tr>
                            <th>交易对</th>
                            <th>方向</th>
                            <th>数量</th>
                            <th>开仓价格</th>
                            <th>当前价格</th>
                            <th>杠杆</th>
                            <th>未实现盈亏</th>
                            <th>盈亏比例</th>
                            <th>保证金</th>
                            <th>强平价格</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="positionsTableBody">
                        <tr>
                            <td colspan="11" class="text-center">
                                <div class="loading-placeholder">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>加载持仓数据中...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 持仓详情模态框 -->
    <div class="modal-overlay" id="positionDetailModal" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-header">
                <h3 id="positionDetailTitle">持仓详情</h3>
                <button class="modal-close" onclick="hidePositionDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="position-detail-content" id="positionDetailContent">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hidePositionDetail()">关闭</button>
                <button class="btn btn-warning" id="adjustPositionBtn">调整持仓</button>
                <button class="btn btn-danger" id="closePositionBtn">平仓</button>
            </div>
        </div>
    </div>

    <!-- 平仓确认模态框 -->
    <div class="modal-overlay" id="closePositionModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>确认平仓</h3>
                <button class="modal-close" onclick="hideClosePositionModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="close-position-form">
                    <div class="form-group">
                        <label>交易对:</label>
                        <span id="closeSymbol">--</span>
                    </div>
                    <div class="form-group">
                        <label>当前持仓:</label>
                        <span id="closeCurrentAmount">--</span>
                    </div>
                    <div class="form-group">
                        <label>平仓数量:</label>
                        <div class="input-group">
                            <input type="number" id="closeAmount" class="form-control" placeholder="留空表示全部平仓">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" onclick="setCloseAmountAll()">全部</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>预估盈亏:</label>
                        <span id="estimatedPnl" class="estimated-pnl">--</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideClosePositionModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmClosePosition()">确认平仓</button>
            </div>
        </div>
    </div>

    <!-- 手动交易模态框 -->
    <div class="modal-overlay" id="manualTradeModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>手动交易</h3>
                <button class="modal-close" onclick="hideManualTradeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="manual-trade-form">
                    <div class="form-group">
                        <label>交易对:</label>
                        <select id="tradeSymbol" class="form-control">
                            <option value="">选择交易对</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>方向:</label>
                        <div class="btn-group trade-side-group">
                            <button class="btn btn-outline-success" data-side="buy">做多</button>
                            <button class="btn btn-outline-danger" data-side="sell">做空</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>数量:</label>
                        <input type="number" id="tradeAmount" class="form-control" placeholder="输入交易数量">
                    </div>
                    <div class="form-group">
                        <label>订单类型:</label>
                        <select id="orderType" class="form-control">
                            <option value="market">市价单</option>
                            <option value="limit">限价单</option>
                        </select>
                    </div>
                    <div class="form-group" id="priceGroup" style="display: none;">
                        <label>价格:</label>
                        <input type="number" id="tradePrice" class="form-control" placeholder="输入限价">
                    </div>
                    <div class="form-group">
                        <label>杠杆:</label>
                        <input type="number" id="tradeLeverage" class="form-control" value="10" min="1" max="100">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideManualTradeModal()">取消</button>
                <button class="btn btn-primary" onclick="submitManualTrade()">提交订单</button>
            </div>
        </div>
    </div>

    <!-- 快速操作按钮 -->
    <div class="floating-actions">
        <button class="fab-btn" onclick="showManualTradeModal()" title="手动交易">
            <i class="fas fa-plus"></i>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 持仓管理页面样式 */
.positions-container {
    padding: var(--spacing-lg);
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.overview-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
}

.card-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-info);
    border-radius: var(--radius-md);
    color: white;
    font-size: 1.25rem;
}

.card-content h3 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    font-family: var(--font-mono);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.table-container {
    overflow-x: auto;
}

.positions-table {
    min-width: 1200px;
}

.positions-table th {
    white-space: nowrap;
}

.positions-table td {
    white-space: nowrap;
    vertical-align: middle;
}

.position-side {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.position-side.long {
    background-color: var(--color-success);
    color: white;
}

.position-side.short {
    background-color: var(--color-error);
    color: white;
}

.pnl-positive {
    color: var(--color-success);
}

.pnl-negative {
    color: var(--color-error);
}

.position-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-xs {
    padding: var(--spacing-xs);
    font-size: 0.75rem;
    min-width: auto;
}

.modal-lg {
    max-width: 800px;
}

.position-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.detail-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
}

.detail-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.detail-label {
    color: var(--text-secondary);
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
}

.close-position-form,
.manual-trade-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-control {
    padding: var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
}

.btn-group {
    display: flex;
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right: none;
}

.btn-group .btn:last-child {
    border-right: 1px solid var(--border-primary);
}

.btn-group .btn.active {
    background-color: var(--color-info);
    border-color: var(--color-info);
    color: white;
}

.trade-side-group .btn[data-side="buy"].active {
    background-color: var(--color-success);
    border-color: var(--color-success);
}

.trade-side-group .btn[data-side="sell"].active {
    background-color: var(--color-error);
    border-color: var(--color-error);
}

.estimated-pnl {
    font-family: var(--font-mono);
    font-weight: 600;
}

.floating-actions {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 100;
}

.fab-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--color-info);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.fab-btn:hover {
    background-color: #1a5fb4;
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .position-detail-content {
        grid-template-columns: 1fr;
    }
    
    .header-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/positions.js') }}"></script>
{% endblock %}
