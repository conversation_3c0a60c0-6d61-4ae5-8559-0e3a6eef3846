{% extends "base.html" %}

{% block title %}仪表板 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<span class="breadcrumb-item">仪表板</span>
{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">交易仪表板</h1>
    <p class="page-description">实时监控账户状态、持仓信息和AI决策</p>
</div>

<!-- 顶部统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-header">
            <h3 class="stat-title">账户总资产</h3>
            <i class="fas fa-wallet stat-icon"></i>
        </div>
        <div class="stat-value" id="totalBalance">
            <span class="loading">加载中...</span>
        </div>
        <div class="stat-change" id="balanceChange">
            <span class="change-value">--</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <h3 class="stat-title">可用余额</h3>
            <i class="fas fa-coins stat-icon"></i>
        </div>
        <div class="stat-value" id="availableBalance">
            <span class="loading">加载中...</span>
        </div>
        <div class="stat-change" id="availableChange">
            <span class="change-value">--</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <h3 class="stat-title">当日盈亏</h3>
            <i class="fas fa-chart-line stat-icon"></i>
        </div>
        <div class="stat-value" id="dailyPnl">
            <span class="loading">加载中...</span>
        </div>
        <div class="stat-change" id="dailyPnlPercent">
            <span class="change-value">--</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <h3 class="stat-title">持仓数量</h3>
            <i class="fas fa-layer-group stat-icon"></i>
        </div>
        <div class="stat-value" id="totalPositions">
            <span class="loading">加载中...</span>
        </div>
        <div class="stat-change" id="positionsChange">
            <span class="change-value">--</span>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
    <!-- 左侧内容 -->
    <div>
        <!-- 持仓概览 -->
        <div class="ant-card">
            <div class="ant-card-head">
                <h3 class="ant-card-head-title">
                    <i class="fas fa-chart-pie"></i>
                    持仓概览
                </h3>
            </div>
            <div class="ant-card-body">
                <div class="positions-summary" id="positionsSummary">
                    <div style="text-align: center; padding: 40px; color: var(--ant-text-secondary);">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 16px;"></i>
                        <div>加载持仓数据中...</div>
                    </div>
                </div>
            </div>
        </div>

            <!-- 风险监控 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        风险监控
                    </h3>
                    <div class="risk-indicator" id="riskIndicator">
                        <span class="risk-level">检查中...</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="risk-metrics" id="riskMetrics">
                        <div class="risk-item">
                            <span class="risk-label">敞口比率:</span>
                            <span class="risk-value" id="exposureRatio">--%</span>
                        </div>
                        <div class="risk-item">
                            <span class="risk-label">保证金使用率:</span>
                            <span class="risk-value" id="marginUsage">--%</span>
                        </div>
                        <div class="risk-item">
                            <span class="risk-label">紧急模式:</span>
                            <span class="risk-value" id="emergencyMode">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="dashboard-right">
            <!-- AI决策历史 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-brain"></i>
                        AI决策历史
                    </h3>
                    <a href="/ai-decisions" class="btn btn-secondary">
                        <i class="fas fa-external-link-alt"></i>
                        查看全部
                    </a>
                </div>
                <div class="card-body">
                    <div class="ai-decisions-list" id="aiDecisionsList">
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载AI决策数据中...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server"></i>
                        系统状态
                    </h3>
                    <div class="system-controls">
                        <button class="btn btn-sm" id="refreshSystemBtn" onclick="refreshSystemStatus()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="system-status" id="systemStatusDetails">
                        <div class="status-item">
                            <span class="status-label">交易引擎:</span>
                            <span class="status-value" id="tradingEngineStatus">检查中...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">AI开仓引擎:</span>
                            <span class="status-value" id="openingEngineStatus">检查中...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">AI持仓引擎:</span>
                            <span class="status-value" id="positionEngineStatus">检查中...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">市场数据:</span>
                            <span class="status-value" id="marketDataStatus">检查中...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">运行时间:</span>
                            <span class="status-value" id="systemUptime">--:--:--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时日志 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-terminal"></i>
                实时日志
            </h3>
            <div class="log-controls">
                <button class="btn btn-sm" onclick="clearLogs()">
                    <i class="fas fa-trash"></i>
                    清空
                </button>
                <button class="btn btn-sm" onclick="toggleAutoScroll()" id="autoScrollBtn">
                    <i class="fas fa-arrow-down"></i>
                    自动滚动
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="console-log" id="consoleLog">
                <div class="log-entry log-info">
                    <span class="log-time" id="systemStartTime">[--:--:--]</span>
                    <span class="log-level">INFO</span>
                    <span class="log-message">系统启动完成，等待数据加载...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- 使用Ant Design主题，无需额外CSS -->
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>
{% endblock %}
