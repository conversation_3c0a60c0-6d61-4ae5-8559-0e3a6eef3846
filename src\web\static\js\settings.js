/**
 * DeepSeek量化交易系统 - 系统设置页面JavaScript
 * 
 * 功能包括：
 * 1. 标签页切换
 * 2. 交易所配置管理
 * 3. 交易参数设置
 * 4. 风险控制参数
 * 5. 交易对选择
 * 6. AI引擎设置
 */

class SettingsManager {
    constructor() {
        this.currentTab = 'exchange';
        this.selectedSymbols = new Set();
        this.availableSymbols = [];

        // 创建apiRequest的本地引用
        this.apiRequest = null;

        this.init();
    }

    /**
     * 初始化设置管理器
     */
    init() {
        console.log('[Settings] 初始化设置管理器');

        this.initTabSwitching();
        this.initExchangeSettings();
        this.initTradingParameters();
        this.initRiskParameters();
        this.initSymbolSelection();
        this.initAISettings();

        // 延迟加载设置，确保apiRequest函数已准备好
        this.waitForApiAndLoadSettings();

        console.log('[Settings] 设置管理器初始化完成');
    }

    /**
     * 等待API函数准备好后加载设置
     */
    async waitForApiAndLoadSettings() {
        // 最多等待10秒，增加等待时间
        const maxWaitTime = 10000;
        const checkInterval = 50; // 减少检查间隔
        let waitedTime = 0;

        while (!window.apiRequest && waitedTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitedTime += checkInterval;
        }

        if (window.apiRequest) {
            console.log('[Settings] API函数已准备好，开始加载设置');
            this.apiRequest = window.apiRequest;
            this.loadCurrentSettings();
        } else {
            console.warn('[Settings] API函数等待超时，使用默认设置');
            // 即使超时也尝试加载，可能函数已经可用但检测有问题
            setTimeout(() => {
                if (window.apiRequest) {
                    console.log('[Settings] 延迟检测到API函数，开始加载设置');
                    this.apiRequest = window.apiRequest;
                    this.loadCurrentSettings();
                }
            }, 1000);
        }
    }

    /**
     * 初始化标签页切换
     */
    initTabSwitching() {
        const tabs = document.querySelectorAll('.nav-tab');
        const contents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.dataset.tab;
                this.switchTab(tabId);
            });
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabId) {
        // 更新标签按钮状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabId}-tab`).classList.add('active');

        this.currentTab = tabId;
        console.log(`[Settings] 切换到标签页: ${tabId}`);
    }

    /**
     * 初始化交易所设置
     */
    initExchangeSettings() {
        const exchangeSelect = document.getElementById('exchangeName');
        const passphraseGroup = document.getElementById('passphraseGroup');
        const exchangeForm = document.getElementById('exchangeForm');

        // 交易所选择变化时显示/隐藏OKX特有字段
        if (exchangeSelect && passphraseGroup) {
            exchangeSelect.addEventListener('change', (e) => {
                const exchange = e.target.value;
                if (exchange === 'okx') {
                    passphraseGroup.style.display = 'block';
                } else {
                    passphraseGroup.style.display = 'none';
                }
            });
        }

        // 表单提交
        if (exchangeForm) {
            exchangeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveExchangeConfig();
            });
        }
    }

    /**
     * 初始化交易参数设置
     */
    initTradingParameters() {
        const tradingForm = document.getElementById('tradingForm');
        
        if (tradingForm) {
            tradingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveTradingParameters();
            });
        }
    }

    /**
     * 初始化风险控制参数
     */
    initRiskParameters() {
        const riskForm = document.getElementById('riskForm');
        
        if (riskForm) {
            riskForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveRiskParameters();
            });
        }
    }

    /**
     * 初始化交易对选择
     */
    initSymbolSelection() {
        const searchInput = document.getElementById('symbolSearch');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterSymbols(e.target.value);
            });
        }
    }

    /**
     * 初始化AI设置
     */
    initAISettings() {
        const aiForm = document.getElementById('aiForm');
        
        if (aiForm) {
            aiForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveAISettings();
            });
        }
    }

    /**
     * 加载当前设置
     */
    async loadCurrentSettings() {
        try {
            console.log('[Settings] 加载当前设置...');
            
            // 加载交易所配置
            await this.loadExchangeConfig();
            
            // 加载交易参数
            await this.loadTradingParameters();
            
            // 加载风险参数
            await this.loadRiskParameters();
            
            // 加载已选择的交易对
            await this.loadSelectedSymbols();
            
            // 加载AI设置
            await this.loadAISettings();
            
            console.log('[Settings] 设置加载完成');
        } catch (error) {
            console.error('[Settings] 加载设置失败:', error);
            this.showNotification('加载设置失败', 'error');
        }
    }

    /**
     * 加载交易所配置
     */
    async loadExchangeConfig() {
        try {
            // 确保apiRequest函数可用
            if (!this.apiRequest) {
                console.warn('[Settings] apiRequest函数未定义，跳过加载交易所配置');
                return;
            }

            const response = await this.apiRequest('/api/config/exchange');
            if (response.success && response.data) {
                const config = response.data;
                
                // 安全地设置表单值
                const exchangeNameEl = document.getElementById('exchangeName');
                const apiKeyEl = document.getElementById('apiKey');
                const secretKeyEl = document.getElementById('secretKey');
                const passphraseEl = document.getElementById('passphrase');
                const sandboxModeEl = document.getElementById('sandboxMode');

                if (exchangeNameEl) exchangeNameEl.value = config.exchange_name || '';
                if (apiKeyEl) apiKeyEl.value = config.api_key || '';
                if (secretKeyEl) secretKeyEl.value = config.secret_key || '';
                if (passphraseEl) passphraseEl.value = config.passphrase || '';
                if (sandboxModeEl) sandboxModeEl.checked = config.sandbox_mode !== false;

                // 根据交易所类型显示相应字段
                const exchange = config.exchange_name || '';
                const passphraseGroup = document.getElementById('passphraseGroup');
                if (passphraseGroup) {
                    if (exchange === 'okx') {
                        passphraseGroup.style.display = 'block';
                    } else {
                        passphraseGroup.style.display = 'none';
                    }
                }
                
                // 更新连接状态
                this.updateConnectionStatus(config.is_connected || false);
            }
        } catch (error) {
            console.error('[Settings] 加载交易所配置失败:', error);
        }
    }

    /**
     * 保存交易所配置
     */
    async saveExchangeConfig() {
        try {
            // 安全地获取表单值
            const exchangeNameEl = document.getElementById('exchangeName');
            const apiKeyEl = document.getElementById('apiKey');
            const secretKeyEl = document.getElementById('secretKey');
            const passphraseEl = document.getElementById('passphrase');
            const sandboxModeEl = document.getElementById('sandboxMode');

            if (!exchangeNameEl || !apiKeyEl || !secretKeyEl) {
                throw new Error('必需的表单元素未找到');
            }

            const formData = {
                exchange_name: exchangeNameEl.value,
                api_key: apiKeyEl.value,
                secret_key: secretKeyEl.value,
                passphrase: passphraseEl ? passphraseEl.value : '',
                sandbox_mode: sandboxModeEl ? sandboxModeEl.checked : true
            };

            console.log('[Settings] 保存交易所配置...');
            const response = await this.apiRequest('/api/config/exchange', 'POST', formData);
            
            if (response.success) {
                this.showNotification('交易所配置保存成功', 'success');
                await this.loadExchangeConfig(); // 重新加载以更新状态
            } else {
                this.showNotification(response.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 保存交易所配置失败:', error);
            this.showNotification('保存交易所配置失败', 'error');
        }
    }

    /**
     * 测试交易所连接
     */
    async testConnection() {
        try {
            console.log('[Settings] 测试交易所连接...');
            this.showNotification('正在测试连接...', 'info');
            
            const response = await apiRequest('/api/config/exchange/test', 'POST');
            
            if (response.success) {
                this.showNotification('连接测试成功', 'success');
                this.updateConnectionStatus(true);
            } else {
                this.showNotification(response.message || '连接测试失败', 'error');
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('[Settings] 连接测试失败:', error);
            this.showNotification('连接测试失败', 'error');
            this.updateConnectionStatus(false);
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(isConnected) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('.status-text');
            
            if (isConnected) {
                indicator.className = 'status-indicator connected';
                text.textContent = '已连接';
            } else {
                indicator.className = 'status-indicator disconnected';
                text.textContent = '未连接';
            }
        }
    }

    /**
     * 加载交易参数
     */
    async loadTradingParameters() {
        try {
            // 确保apiRequest函数可用
            if (typeof apiRequest === 'undefined') {
                console.warn('[Settings] apiRequest函数未定义，跳过加载交易参数');
                return;
            }

            const response = await apiRequest('/api/config/trading');
            if (response.success && response.data) {
                const params = response.data;
                
                document.getElementById('maxLeverage').value = params.max_leverage || 10;
                document.getElementById('maxPositionRatio').value = params.max_position_ratio || 50;
                document.getElementById('openingThreshold').value = params.opening_confidence_threshold || 70;
                document.getElementById('positionThreshold').value = params.position_confidence_threshold || 60;
                document.getElementById('stopLoss').value = params.default_stop_loss_percentage || 5;
                document.getElementById('takeProfit').value = params.default_take_profit_percentage || 10;
            }
        } catch (error) {
            console.error('[Settings] 加载交易参数失败:', error);
        }
    }

    /**
     * 保存交易参数
     */
    async saveTradingParameters() {
        try {
            const formData = {
                max_leverage: parseInt(document.getElementById('maxLeverage').value),
                max_position_ratio: parseFloat(document.getElementById('maxPositionRatio').value),
                opening_confidence_threshold: parseInt(document.getElementById('openingThreshold').value),
                position_confidence_threshold: parseInt(document.getElementById('positionThreshold').value),
                default_stop_loss_percentage: parseFloat(document.getElementById('stopLoss').value),
                default_take_profit_percentage: parseFloat(document.getElementById('takeProfit').value)
            };

            console.log('[Settings] 保存交易参数...');
            const response = await apiRequest('/api/config/trading', 'POST', formData);
            
            if (response.success) {
                this.showNotification('交易参数保存成功', 'success');
            } else {
                this.showNotification(response.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 保存交易参数失败:', error);
            this.showNotification('保存交易参数失败', 'error');
        }
    }

    /**
     * 重置交易参数为默认值
     */
    resetTradingParams() {
        document.getElementById('maxLeverage').value = 10;
        document.getElementById('maxPositionRatio').value = 50;
        document.getElementById('openingThreshold').value = 70;
        document.getElementById('positionThreshold').value = 60;
        document.getElementById('stopLoss').value = 5;
        document.getElementById('takeProfit').value = 10;
        
        this.showNotification('已重置为默认参数', 'info');
    }

    /**
     * 加载风险参数
     */
    async loadRiskParameters() {
        try {
            const response = await apiRequest('/api/config/risk');
            if (response.success && response.data) {
                const params = response.data;

                document.getElementById('systemMaxLeverage').value = params.max_leverage || 20;
                document.getElementById('maxExposureRatio').value = params.max_exposure_ratio || 80;
                document.getElementById('minBalanceThreshold').value = params.min_balance_threshold || 100;
                document.getElementById('maxDrawdown').value = params.max_drawdown || 20;
            }
        } catch (error) {
            console.error('[Settings] 加载风险参数失败:', error);
        }
    }

    /**
     * 保存风险参数
     */
    async saveRiskParameters() {
        try {
            const formData = {
                max_leverage: parseInt(document.getElementById('systemMaxLeverage').value),
                max_exposure_ratio: parseFloat(document.getElementById('maxExposureRatio').value),
                min_balance_threshold: parseFloat(document.getElementById('minBalanceThreshold').value),
                max_drawdown: parseFloat(document.getElementById('maxDrawdown').value)
            };

            console.log('[Settings] 保存风险参数...');
            const response = await apiRequest('/api/config/risk', 'POST', formData);

            if (response.success) {
                this.showNotification('风险参数保存成功', 'success');
            } else {
                this.showNotification(response.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 保存风险参数失败:', error);
            this.showNotification('保存风险参数失败', 'error');
        }
    }

    /**
     * 重置风险参数为默认值
     */
    resetRiskParams() {
        document.getElementById('systemMaxLeverage').value = 20;
        document.getElementById('maxExposureRatio').value = 80;
        document.getElementById('minBalanceThreshold').value = 100;
        document.getElementById('maxDrawdown').value = 20;

        this.showNotification('已重置为默认风险参数', 'info');
    }

    /**
     * 加载已选择的交易对
     */
    async loadSelectedSymbols() {
        try {
            const response = await apiRequest('/api/config/symbols');
            if (response.success && response.data) {
                this.selectedSymbols = new Set(response.data.symbols || []);
                this.updateSelectedSymbolsDisplay();
            }
        } catch (error) {
            console.error('[Settings] 加载交易对失败:', error);
        }
    }

    /**
     * 加载可用交易对列表
     */
    async loadAvailableSymbols() {
        try {
            console.log('[Settings] 加载可用交易对...');
            this.showSymbolsLoading(true);

            const response = await apiRequest('/api/config/symbols/available');
            if (response.success && response.data) {
                this.availableSymbols = response.data.symbols || [];
                this.renderSymbolsGrid();
                this.showNotification('交易对列表刷新成功', 'success');
            } else {
                this.showNotification(response.message || '获取交易对失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 加载可用交易对失败:', error);
            this.showNotification('加载交易对失败', 'error');
        } finally {
            this.showSymbolsLoading(false);
        }
    }

    /**
     * 渲染交易对网格
     */
    renderSymbolsGrid(filteredSymbols = null) {
        const grid = document.getElementById('symbolsGrid');
        if (!grid) return;

        const symbols = filteredSymbols || this.availableSymbols;

        if (symbols.length === 0) {
            grid.innerHTML = '<div class="empty-state">暂无可用交易对</div>';
            return;
        }

        grid.innerHTML = symbols.map(symbol => `
            <div class="symbol-item ${this.selectedSymbols.has(symbol) ? 'selected' : ''}"
                 data-symbol="${symbol}" onclick="settingsManager.toggleSymbol('${symbol}')">
                <span class="symbol-name">${symbol}</span>
                <span class="symbol-check">✓</span>
            </div>
        `).join('');
    }

    /**
     * 切换交易对选择状态
     */
    toggleSymbol(symbol) {
        if (this.selectedSymbols.has(symbol)) {
            this.selectedSymbols.delete(symbol);
        } else {
            this.selectedSymbols.add(symbol);
        }

        this.renderSymbolsGrid();
        this.updateSelectedSymbolsDisplay();
    }

    /**
     * 更新已选择交易对显示
     */
    updateSelectedSymbolsDisplay() {
        const countElement = document.getElementById('selectedCount');
        const listElement = document.getElementById('selectedList');

        if (countElement) {
            countElement.textContent = this.selectedSymbols.size;
        }

        if (listElement) {
            if (this.selectedSymbols.size === 0) {
                listElement.innerHTML = '<div class="empty-state">暂未选择交易对</div>';
            } else {
                listElement.innerHTML = Array.from(this.selectedSymbols).map(symbol => `
                    <div class="selected-symbol-item">
                        <span>${symbol}</span>
                        <button onclick="settingsManager.toggleSymbol('${symbol}')" class="remove-btn">×</button>
                    </div>
                `).join('');
            }
        }
    }

    /**
     * 筛选交易对
     */
    filterSymbols(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderSymbolsGrid();
            return;
        }

        const filtered = this.availableSymbols.filter(symbol =>
            symbol.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.renderSymbolsGrid(filtered);
    }

    /**
     * 清空所有选择的交易对
     */
    clearAllSymbols() {
        this.selectedSymbols.clear();
        this.renderSymbolsGrid();
        this.updateSelectedSymbolsDisplay();
        this.showNotification('已清空所有选择', 'info');
    }

    /**
     * 保存选择的交易对
     */
    async saveSelectedSymbols() {
        try {
            const formData = {
                symbols: Array.from(this.selectedSymbols)
            };

            console.log('[Settings] 保存交易对选择...');
            const response = await apiRequest('/api/config/symbols', 'POST', formData);

            if (response.success) {
                this.showNotification(`已保存 ${this.selectedSymbols.size} 个交易对`, 'success');
            } else {
                this.showNotification(response.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 保存交易对失败:', error);
            this.showNotification('保存交易对失败', 'error');
        }
    }

    /**
     * 显示/隐藏交易对加载状态
     */
    showSymbolsLoading(isLoading) {
        const grid = document.getElementById('symbolsGrid');
        if (!grid) return;

        if (isLoading) {
            grid.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>加载交易对列表中...</span>
                </div>
            `;
        }
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 添加关闭事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // 自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        console.log(`[Settings] ${type.toUpperCase()}: ${message}`);
    }

    /**
     * 加载AI设置
     */
    async loadAISettings() {
        try {
            const response = await apiRequest('/api/config/ai');
            if (response.success && response.data) {
                const settings = response.data;

                document.getElementById('deepseekApiKey').value = settings.deepseek_api_key || '';
                document.getElementById('analysisInterval').value = settings.analysis_interval || 300;
                document.getElementById('lookbackPeriod').value = settings.lookback_period || 24;
                document.getElementById('enableOpeningEngine').checked = settings.enable_opening_engine !== false;
                document.getElementById('enablePositionEngine').checked = settings.enable_position_engine !== false;
            }
        } catch (error) {
            console.error('[Settings] 加载AI设置失败:', error);
        }
    }

    /**
     * 保存AI设置
     */
    async saveAISettings() {
        try {
            const formData = {
                deepseek_api_key: document.getElementById('deepseekApiKey').value,
                analysis_interval: parseInt(document.getElementById('analysisInterval').value),
                lookback_period: parseInt(document.getElementById('lookbackPeriod').value),
                enable_opening_engine: document.getElementById('enableOpeningEngine').checked,
                enable_position_engine: document.getElementById('enablePositionEngine').checked
            };

            console.log('[Settings] 保存AI设置...');
            const response = await apiRequest('/api/config/ai', 'POST', formData);

            if (response.success) {
                this.showNotification('AI设置保存成功', 'success');
            } else {
                this.showNotification(response.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] 保存AI设置失败:', error);
            this.showNotification('保存AI设置失败', 'error');
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        try {
            console.log('[Settings] 测试AI连接...');
            this.showNotification('正在测试AI连接...', 'info');

            const response = await apiRequest('/api/config/ai/test', 'POST');

            if (response.success) {
                this.showNotification('AI连接测试成功', 'success');
            } else {
                this.showNotification(response.message || 'AI连接测试失败', 'error');
            }
        } catch (error) {
            console.error('[Settings] AI连接测试失败:', error);
            this.showNotification('AI连接测试失败', 'error');
        }
    }
}

// 全局函数，供HTML调用
window.testConnection = function() {
    if (window.settingsManager) {
        window.settingsManager.testConnection();
    }
};

window.resetTradingParams = function() {
    if (window.settingsManager) {
        window.settingsManager.resetTradingParams();
    }
};

window.resetRiskParams = function() {
    if (window.settingsManager) {
        window.settingsManager.resetRiskParams();
    }
};

window.loadAvailableSymbols = function() {
    if (window.settingsManager) {
        window.settingsManager.loadAvailableSymbols();
    }
};

window.clearAllSymbols = function() {
    if (window.settingsManager) {
        window.settingsManager.clearAllSymbols();
    }
};

window.saveSelectedSymbols = function() {
    if (window.settingsManager) {
        window.settingsManager.saveSelectedSymbols();
    }
};

window.testAIConnection = function() {
    if (window.settingsManager) {
        window.settingsManager.testAIConnection();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[Settings] 页面加载完成，初始化设置管理器');
    window.settingsManager = new SettingsManager();
});
